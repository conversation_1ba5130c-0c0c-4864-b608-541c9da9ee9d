using NUnit.Framework;
using OpenQA.Selenium.Support.UI;
using DrMuscle.UITests.Helpers;

namespace DrMuscle.UITests.Tests
{
    [TestFixture]
    [User<PERSON><PERSON><PERSON>(TestCategories.PreInstallation)]
    [Description("Basic smoke tests to verify app launches and is responsive")]
    public class SmokeTest : AppiumSetup
    {
        [Test]
        [Description("Verify app launches successfully and driver is properly initialized")]
        public void AppLaunches()
        {
            // Simple test to verify app launches
            Assert.That(Driver, Is.Not.Null, "Driver should be initialized");
            
            // Take a screenshot
            var screenshot = Driver?.GetScreenshot();
            if (screenshot != null)
            {
                var screenshotPath = Path.Combine(TestContext.CurrentContext.WorkDirectory, "app-launched.png");
                screenshot.SaveAsFile(screenshotPath);
                TestContext.AddTestAttachment(screenshotPath, "App launched screenshot");
            }
            
            // Wait a moment for initial UI to load
            Thread.Sleep(2000);
            
            // Check if we can interact with the driver
            var pageSource = Driver?.PageSource;
            Assert.That(pageSource, Is.Not.Null.And.Not.Empty, "Page source should be available");
            
            Assert.Pass("App launched successfully via Appium");
        }
        
        [Test]
        [Description("Verify app UI is responsive and elements can be found")]
        public void CanFindWelcomeScreen()
        {
            // This test will look for elements on the welcome screen
            // We'll need to add AutomationIds to the MAUI app for this to work properly
            
            try
            {
                // Try to find any element to verify the app is responsive
                var elements = Driver?.FindElements(By.XPath("//*"));
                Assert.That(elements?.Count, Is.GreaterThan(0), "Should find at least one element");
                
                Console.WriteLine($"Found {elements?.Count} elements on the screen");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error finding elements: {ex.Message}");
                // Take a screenshot on failure
                var screenshot = Driver?.GetScreenshot();
                if (screenshot != null)
                {
                    var screenshotPath = Path.Combine(TestContext.CurrentContext.WorkDirectory, "welcome-screen-error.png");
                    screenshot.SaveAsFile(screenshotPath);
                    TestContext.AddTestAttachment(screenshotPath, "Error screenshot");
                }
                throw;
            }
        }
    }
}