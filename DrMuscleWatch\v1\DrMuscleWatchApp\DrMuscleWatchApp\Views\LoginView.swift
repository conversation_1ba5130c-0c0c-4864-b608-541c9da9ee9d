import SwiftUI
import AuthenticationServices

/// View for the login screen on watchOS
struct LoginView: View {
    /// The view model for the login screen
    @StateObject private var viewModel: LoginViewModel

    /// The authentication manager
    @EnvironmentObject private var authManager: AuthenticationManager
    
    /// Initializer with optional view model for testing
    init(viewModel: LoginViewModel? = nil) {
        _viewModel = StateObject(wrappedValue: viewModel ?? LoginViewModel())
    }

    var body: some View {
        ScrollView {
            VStack(spacing: 12) {
                // App logo
                Image(systemName: "dumbbell.fill")
                    .resizable()
                    .aspectRatio(contentMode: .fit)
                    .frame(width: 40, height: 40)
                    .foregroundColor(.yellow)

                // App name
                Text("Dr. Muscle")
                    .font(.title3)
                    .fontWeight(.bold)
                    .foregroundColor(.white)

                // Welcome message
                Text("Watch App")
                    .font(.caption)
                    .foregroundColor(.gray)

                // Sign in with Apple button
                Button(action: {
                    viewModel.startSignInWithApple()
                }) {
                    HStack {
                        Image(systemName: "applelogo")
                            .font(.caption)
                        Text("Sign in")
                            .font(.caption2)
                    }
                }
                .buttonStyle(.borderedProminent)
                .tint(.black)
                .foregroundColor(.white)
                .disabled(viewModel.isAuthenticating)

                // Error message
                if let errorMessage = viewModel.errorMessage {
                    Text(errorMessage)
                        .font(.caption2)
                        .foregroundColor(.red)
                        .multilineTextAlignment(.center)
                }

                // Loading indicator
                if viewModel.isAuthenticating {
                    ProgressView()
                        .progressViewStyle(CircularProgressViewStyle(tint: .yellow))
                        .scaleEffect(0.8)
                }
            }
            .padding()
        }
    }
}

#Preview {
    LoginView()
        .environmentObject(AuthenticationManager.shared)
}
