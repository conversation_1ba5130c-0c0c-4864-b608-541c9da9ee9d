# Sign in with Apple Implementation Guide for watchOS

## Overview

This guide provides step-by-step instructions for implementing Sign in with Apple authentication in the Dr. Muscle Watch app, replacing the current mock authentication.

## Current State

- **Mock Implementation**: The app currently uses a "Quick Sign In" button that creates fake user credentials
- **No Real Authentication**: The `AuthenticationManager.signIn()` method creates mock data instead of communicating with the backend
- **Tests Written**: Following TDD, we've written comprehensive tests that are currently failing

## Implementation Steps

### Phase 1: Update AuthenticationManager (Make Tests Pass)

#### 1.1 Add ASAuthorizationControllerDelegate

Update `AuthenticationManager.swift` to implement Sign in with Apple:

```swift
import Foundation
import Security
import AuthenticationServices

/// Manages authentication state and operations for watchOS
class AuthenticationManager: NSObject, ObservableObject {
    /// Shared instance for the entire application
    static let shared = AuthenticationManager()
    
    /// Published property indicating whether the user is authenticated
    @Published var isAuthenticated: Bool = false
    
    /// The current authenticated user
    @Published var currentUser: UserInfosModel?
    
    /// Error message if authentication fails
    @Published var authError: String?
    
    /// Private initializer to enforce singleton pattern
    private override init() {
        super.init()
        loadAuthState()
    }
    
    // MARK: - Sign in with Apple Methods
    
    /// Handles successful Sign in with Apple
    func handleSignInWithApple(credential: ASAuthorizationAppleIDCredential) async {
        guard let identityToken = credential.identityToken,
              let tokenString = String(data: identityToken, encoding: .utf8) else {
            await MainActor.run {
                self.authError = "Invalid credentials received from Apple"
            }
            return
        }
        
        do {
            // Call backend API to validate Apple token
            let userInfo = try await DrMuscleAPIClient.shared.signInWithApple(idToken: tokenString)
            
            await MainActor.run {
                self.storeAuthState(userInfo: userInfo)
                self.authError = nil
            }
        } catch {
            await MainActor.run {
                self.authError = "Could not connect to Dr. Muscle. Please try again."
            }
        }
    }
    
    /// Handles Sign in with Apple errors
    func handleSignInWithAppleError(_ error: Error) async {
        let nsError = error as NSError
        
        await MainActor.run {
            if nsError.domain == ASAuthorizationError.errorDomain {
                switch nsError.code {
                case ASAuthorizationError.canceled.rawValue:
                    // User cancelled - don't show error
                    self.authError = nil
                case ASAuthorizationError.failed.rawValue:
                    self.authError = "Sign in failed. Please try again."
                case ASAuthorizationError.notHandled.rawValue:
                    self.authError = "Sign in not available. Please try again."
                default:
                    self.authError = "Sign in failed. Please try again."
                }
            } else {
                self.authError = error.localizedDescription
            }
        }
    }
    
    // ... existing loadAuthState, storeAuthState, clearAuthState methods ...
}

// MARK: - ASAuthorizationControllerDelegate

extension AuthenticationManager: ASAuthorizationControllerDelegate {
    func authorizationController(controller: ASAuthorizationController, 
                                didCompleteWithAuthorization authorization: ASAuthorization) {
        guard let appleIDCredential = authorization.credential as? ASAuthorizationAppleIDCredential else {
            return
        }
        
        Task {
            await handleSignInWithApple(credential: appleIDCredential)
        }
    }
    
    func authorizationController(controller: ASAuthorizationController, 
                                didCompleteWithError error: Error) {
        Task {
            await handleSignInWithAppleError(error)
        }
    }
}
```

### Phase 2: Update LoginViewModel

#### 2.1 Implement Sign in with Apple Flow

Update `LoginViewModel.swift`:

```swift
import Foundation
import SwiftUI
import AuthenticationServices

/// View model for the login screen on watchOS
class LoginViewModel: ObservableObject {
    /// The authentication manager
    private let authManager = AuthenticationManager.shared
    
    /// Whether authentication is in progress
    @Published var isAuthenticating: Bool = false
    
    /// Error message to display
    @Published var errorMessage: String?
    
    /// The authorization controller for Sign in with Apple
    private var authorizationController: ASAuthorizationController?
    
    /// Starts the Sign in with Apple flow
    func startSignInWithApple() {
        isAuthenticating = true
        errorMessage = nil
        
        let request = createAuthorizationRequest()
        authorizationController = ASAuthorizationController(authorizationRequests: [request])
        authorizationController?.delegate = authManager
        authorizationController?.performRequests()
        
        // Monitor auth manager for completion
        Task {
            await monitorAuthenticationCompletion()
        }
    }
    
    /// Creates an authorization request for Sign in with Apple
    func createAuthorizationRequest() -> ASAuthorizationAppleIDRequest {
        let provider = ASAuthorizationAppleIDProvider()
        let request = provider.createRequest()
        request.requestedScopes = [.fullName, .email]
        return request
    }
    
    /// Monitors authentication completion
    private func monitorAuthenticationCompletion() async {
        // Wait a moment for auth to process
        try? await Task.sleep(nanoseconds: 500_000_000) // 0.5 seconds
        
        await MainActor.run {
            self.isAuthenticating = false
            if let error = authManager.authError {
                self.errorMessage = error
            }
        }
    }
    
    // Remove old methods
    // - signIn()
    // - quickSignIn()
}
```

### Phase 3: Update LoginView

#### 3.1 Implement Native Sign in with Apple Button

Update `LoginView.swift`:

```swift
import SwiftUI
import AuthenticationServices

/// View for the login screen on watchOS
struct LoginView: View {
    /// The view model for the login screen
    @StateObject private var viewModel = LoginViewModel()
    
    /// The authentication manager
    @EnvironmentObject private var authManager: AuthenticationManager
    
    var body: some View {
        ScrollView {
            VStack(spacing: 12) {
                // App logo
                Image(systemName: "dumbbell.fill")
                    .resizable()
                    .aspectRatio(contentMode: .fit)
                    .frame(width: 40, height: 40)
                    .foregroundColor(.yellow)
                
                // App name
                Text("Dr. Muscle")
                    .font(.title3)
                    .fontWeight(.bold)
                    .foregroundColor(.white)
                
                // Welcome message
                Text("Watch App")
                    .font(.caption)
                    .foregroundColor(.gray)
                
                // Sign in with Apple button
                SignInWithAppleButton(
                    .signIn,
                    onRequest: { request in
                        // Request is already configured in view model
                    },
                    onCompletion: { result in
                        // Handled by AuthenticationManager delegate
                    }
                )
                .frame(height: 45)
                .signInWithAppleButtonStyle(.whiteOutline)
                .disabled(viewModel.isAuthenticating)
                .onTapGesture {
                    if !viewModel.isAuthenticating {
                        viewModel.startSignInWithApple()
                    }
                }
                
                // Error message
                if let errorMessage = viewModel.errorMessage {
                    Text(errorMessage)
                        .font(.caption2)
                        .foregroundColor(.red)
                        .multilineTextAlignment(.center)
                }
                
                // Loading indicator
                if viewModel.isAuthenticating {
                    ProgressView()
                        .progressViewStyle(CircularProgressViewStyle(tint: .yellow))
                        .scaleEffect(0.8)
                }
            }
            .padding()
        }
    }
}

#Preview {
    LoginView()
        .environmentObject(AuthenticationManager.shared)
}
```

### Phase 4: Update API Client

#### 4.1 Add Sign in with Apple Endpoint

Ensure `DrMuscleAPIClient.swift` includes the authentication endpoint:

```swift
extension DrMuscleAPIClient {
    /// Authenticates with Apple ID token
    func signInWithApple(idToken: String) async throws -> UserInfosModel {
        let endpoint = "api/Account/RegisterWithApple"
        let body = ["token": idToken]
        
        return try await postJSON(route: endpoint, model: body)
    }
}
```

### Phase 5: Clean Up

#### 5.1 Remove Mock Code

1. Remove the `signIn(email:password:)` method from `AuthenticationManager`
2. Remove any mock user creation code
3. Update any references to the old authentication methods

### Phase 6: Run Tests

After implementing all changes:

1. Run the test suite
2. Verify all authentication tests pass
3. Fix any failing tests
4. Update `testing.md` to mark tests as "Passing"

## Testing on Device

### Manual Testing Steps

1. **Clean Install**:
   - Delete the app from the watch
   - Install fresh from Xcode
   - Verify Sign in with Apple button appears

2. **Sign In Flow**:
   - Tap Sign in with Apple button
   - Complete authentication
   - Verify navigation to workout list

3. **Error Scenarios**:
   - Test with no network connection
   - Test cancellation
   - Test with invalid credentials

4. **Persistence**:
   - Sign in successfully
   - Force quit the app
   - Reopen and verify still authenticated

## Troubleshooting

### Common Issues

1. **"Sign in with Apple isn't available"**:
   - Ensure device is signed into iCloud
   - Check entitlements include Sign in with Apple capability

2. **Network errors**:
   - Verify API endpoint is accessible
   - Check token format matches backend expectations

3. **UI not updating**:
   - Ensure @Published properties are updated on main thread
   - Verify environment objects are properly passed

## Security Considerations

1. **Token Storage**:
   - Always use Keychain for sensitive data
   - Never log authentication tokens

2. **Error Messages**:
   - Don't expose sensitive information in errors
   - Use generic messages for authentication failures

3. **Network Security**:
   - Ensure all API calls use HTTPS
   - Validate SSL certificates

## Next Steps

After successful implementation:

1. Update `status.md` to mark AUTH-01 as complete
2. Remove this implementation guide or move to archived docs
3. Proceed with next task in the backlog 