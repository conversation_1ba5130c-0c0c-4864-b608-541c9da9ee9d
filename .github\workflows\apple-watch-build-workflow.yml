# Dr. Muscle Apple Watch Build & Deploy Workflow
# Builds and deploys the standalone Dr. Muscle Watch app to TestFlight
# Integrated with existing CI/CD patterns and Scaleway Mac infrastructure

name: Apple Watch Build & Deploy

concurrency:
  group: watch-build-${{ github.ref }}
  cancel-in-progress: true

permissions:
  contents: read
  issues: write

on:
  workflow_dispatch: # Manual trigger
  push:
    branches:
      - Development_Watch_Carl_v1.1
    paths:
      - 'DrMuscleWatch/**'
      - '.github/workflows/apple-watch-build-workflow.yml'
  pull_request:
    branches:
      - Development_Watch_Carl_v1.1
    paths:
      - 'DrMuscleWatch/**'
      - '.github/workflows/apple-watch-build-workflow.yml'

# Dr. Muscle Watch App Configuration
env:
  # Project Configuration
  PROJECT_NAME: "DrMuscleWatchApp.xcodeproj"
  SCHEME_NAME: "DrMuscleWatchApp-iOS"
  APP_NAME: "DrMuscleWatchApp"
  PROJECT_PATH: "DrMuscleWatch/v1/DrMuscleWatchApp"

  # Bundle Identifiers (matching Apple Developer Portal configuration)
  # iOS companion app and watchOS app use different bundle IDs per Apple convention
  IOS_STUB_BUNDLE_ID: "com.drmaxmuscle.max"
  WATCH_APP_BUNDLE_ID: "com.drmaxmuscle.max.watchkitapp"

  # Provisioning Profile Names (must match names in Apple Developer Portal)
  # iOS and watchOS targets use different provisioning profiles for their respective bundle IDs
  WATCH_APP_PROVISIONING_PROFILE_NAME: "Dr_Muscle_Watch_App_Store_Profile"
  IOS_STUB_PROVISIONING_PROFILE_NAME: "Dr Muscle June 11 26"

jobs:
  # Centralized setup job for shared dependencies and version generation
  setup:
    name: Setup & Version Generation
    runs-on: ubicloud-standard-2
    outputs:
      start-time: ${{ steps.start-time.outputs.start-time }}
      version-code: ${{ steps.version.outputs.version_code }}
      version-name: ${{ steps.version.outputs.version_name }}
      assembly-version: ${{ steps.version.outputs.assembly_version }}
    steps:
    - name: Record workflow start time
      id: start-time
      run: |
        START_TIME=$(date +%s)
        echo "start-time=$START_TIME" >> $GITHUB_OUTPUT
        echo "Workflow started at: $(date -d @$START_TIME)"
    - uses: actions/checkout@v4

    - name: Generate unified version for Watch App
      id: version
      run: |
        # Get current date components for clean versioning
        YEAR=$(date +%Y)
        MONTH=$(date +%m)
        DAY=$(date +%d)
        BUILD_NUMBER=$GITHUB_RUN_NUMBER
        # Create clean 3-part version: 3.YYMM.DDXX (XX = last 2 digits of run)
        YY=$(echo $YEAR | tail -c 3)  # Last 2 digits of year (25 for 2025)
        YYMM="${YY}$(printf "%02d" $((10#$MONTH)))"  # YYMM format (2506 for June 2025)
        RUN_LAST_TWO=$(printf "%02d" $((BUILD_NUMBER % 100)))  # Last 2 digits of run (12 for run 112)
        DD_XX="$(printf "%02d" $((10#$DAY)))${RUN_LAST_TWO}"  # DDXX (1612 for 16th day, run 112)
        # Single clean 3-part version for Watch app
        CLEAN_VERSION="3.${YYMM}.${DD_XX}"
        echo "version_code=$CLEAN_VERSION" >> $GITHUB_OUTPUT
        echo "version_name=$CLEAN_VERSION" >> $GITHUB_OUTPUT
        echo "assembly_version=$CLEAN_VERSION" >> $GITHUB_OUTPUT
        echo "Using clean 3-part version format for Watch app:"
        echo "  Version: $CLEAN_VERSION (3.YYMM.DDXX where XX = last 2 digits of run)"
        echo "  Example: 3.2506.1212 = June 12th, 2025, run #112 → XX=12"
  # Check if runner is already available before waking up server
  check-runner-availability:
    name: Check Runner Availability
    runs-on: ubicloud-standard-2
    needs: setup
    outputs:
      runner-available: ${{ steps.check-runner-status.outputs.runner-available }}
      skip-wake-up: ${{ steps.check-runner-status.outputs.skip-wake-up }}
    steps:
    - name: Check if runner is already available
      id: check-runner-status
      run: |
        echo "🔍 Checking if Scaleway runner is already available..."
        # Install sshpass for SSH connectivity check
        sudo apt-get update -qq && sudo apt-get install -y sshpass
        # SSH connection details from GitHub secrets
        SSH_HOST="${{ secrets.SCALEWAY_MAC_SSH_HOST }}"
        SSH_USER="${{ secrets.SCALEWAY_MAC_SSH_USER }}"
        SSH_PASS="${{ secrets.SCALEWAY_MAC_SSH_PASSWORD }}"
        SSH_OPTIONS="-o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null -o ConnectTimeout=10"
        # Check if SSH credentials are available
        if [ -z "$SSH_HOST" ] || [ -z "$SSH_USER" ] || [ -z "$SSH_PASS" ]; then
          echo "⚠️ SSH credentials not available - will perform full wake-up"
          echo "runner-available=false" >> $GITHUB_OUTPUT
          echo "skip-wake-up=false" >> $GITHUB_OUTPUT
        else
          # Quick check if runner process is already running
          echo "🔍 Checking if runner process is active..."
          RUNNER_CHECK=$(sshpass -p "$SSH_PASS" ssh $SSH_OPTIONS $SSH_USER@$SSH_HOST "ps aux | grep 'Runner.Listener' | grep -v grep" 2>/dev/null || echo "No runner process found")
          if echo "$RUNNER_CHECK" | grep -q "Runner.Listener"; then
            echo "✅ Runner process is already running - skipping wake-up and SSH setup"
            echo "Runner process: $RUNNER_CHECK"
            echo "runner-available=true" >> $GITHUB_OUTPUT
            echo "skip-wake-up=true" >> $GITHUB_OUTPUT
          else
            echo "⚠️ Runner process not found - will perform wake-up and SSH setup"
            echo "runner-available=false" >> $GITHUB_OUTPUT
            echo "skip-wake-up=false" >> $GITHUB_OUTPUT
          fi
        fi
  # Wake up Scaleway Mac mini only if runner is not already available
  wake-scaleway-runner:
    name: Wake Up Scaleway Mac Runner
    if: always()
    runs-on: ubicloud-standard-2
    needs: [setup, check-runner-availability]
    outputs:
      runner-status: ${{ steps.check-runner.outputs.runner-status }}
    steps:
    - name: Check if wake-up is needed
      id: wake-up-check
      run: |
        if [ "${{ needs.check-runner-availability.outputs.skip-wake-up }}" = "true" ]; then
          echo "✅ Runner is already available - skipping wake-up process"
          echo "skip-wake-up=true" >> $GITHUB_OUTPUT
        else
          echo "🔄 Runner not available - proceeding with wake-up process"
          echo "skip-wake-up=false" >> $GITHUB_OUTPUT
        fi
    - name: Wake up Scaleway Mac mini
      if: steps.wake-up-check.outputs.skip-wake-up == 'false'
      env:
        SCW_ACCESS_KEY: ${{ secrets.SCW_ACCESS_KEY }}
        SCW_SECRET_KEY: ${{ secrets.SCW_SECRET_KEY }}
        SCW_DEFAULT_PROJECT_ID: ${{ secrets.SCW_DEFAULT_PROJECT_ID }}
        SCW_DEFAULT_ORGANIZATION_ID: ${{ secrets.SCW_DEFAULT_ORGANIZATION_ID }}
        SCW_DEFAULT_REGION: ${{ secrets.SCW_DEFAULT_REGION }}
        SCW_DEFAULT_ZONE: ${{ secrets.SCW_DEFAULT_ZONE }}
        SCALEWAY_SERVER_ID: ${{ secrets.SCALEWAY_SERVER_ID }}
      run: |
        echo "🚀 Attempting to wake up Scaleway Mac mini for Watch app build..."
        # Set default zone for Apple Silicon API calls
        if [ -n "$SCW_DEFAULT_ZONE" ]; then
          API_ZONE="$SCW_DEFAULT_ZONE"
          echo "✅ Using zone: $API_ZONE"
        else
          API_ZONE="fr-par-3"
          echo "✅ Using default zone: $API_ZONE"
        fi
        # Set Apple Silicon API endpoint
        API_ENDPOINT="https://api.scaleway.com/apple-silicon/v1alpha1/zones/$API_ZONE"
        echo "🔧 Using Apple Silicon API endpoint: $API_ENDPOINT"
        # Check current server status
        echo "🔍 Checking Apple Silicon Mac mini status..."
        SERVER_RESPONSE=$(curl -s -H "X-Auth-Token: $SCW_SECRET_KEY" \
          -H "Content-Type: application/json" \
          "$API_ENDPOINT/servers/$SCALEWAY_SERVER_ID")
        if echo "$SERVER_RESPONSE" | jq -e '.status' >/dev/null 2>&1; then
          SERVER_STATUS=$(echo "$SERVER_RESPONSE" | jq -r '.status')
          SERVER_NAME=$(echo "$SERVER_RESPONSE" | jq -r '.name')
          echo "✅ Found Apple Silicon server '$SERVER_NAME' with status: $SERVER_STATUS"
        else
          echo "❌ Failed to get server status:"
          echo "$SERVER_RESPONSE"
          exit 1
        fi
        echo "📊 Current server status: $SERVER_STATUS"
        # Skip reboot if server is already ready
        if [ "$SERVER_STATUS" = "ready" ]; then
          echo "✅ Server is already ready, skipping reboot to avoid service disruption"
        else
          echo "⚠️ Server not ready (status: $SERVER_STATUS), but skipping reboot for now"
          echo "ℹ️ If runner issues persist, manual reboot may be needed"
        fi
    - name: Wait for GitHub Actions runner to come online
      id: check-runner
      if: steps.wake-up-check.outputs.skip-wake-up == 'false'
      run: |
        echo "⏳ Waiting for GitHub Actions runner to come online..."
        echo "ℹ️ Ensuring runner service is properly configured and running"
        # Server should already be ready, minimal wait for SSH
        echo "⏱️ Waiting 10 seconds for SSH to be ready..."
        sleep 10
        # SSH connection details from GitHub secrets
        SSH_HOST="${{ secrets.SCALEWAY_MAC_SSH_HOST }}"
        SSH_USER="${{ secrets.SCALEWAY_MAC_SSH_USER }}"
        SSH_PASS="${{ secrets.SCALEWAY_MAC_SSH_PASSWORD }}"
        SSH_OPTIONS="-o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null -o ConnectTimeout=30"
        # Validate SSH credentials are available
        if [ -z "$SSH_HOST" ] || [ -z "$SSH_USER" ] || [ -z "$SSH_PASS" ]; then
          echo "❌ SSH credentials not configured in GitHub secrets"
          echo "runner-status=ssh-unavailable" >> $GITHUB_OUTPUT
          exit 0
        fi
        # Install sshpass for password authentication
        echo "📦 Installing sshpass for SSH password authentication..."
        sudo apt-get update -qq && sudo apt-get install -y sshpass
        # Check if runner service is running and fix if needed
        echo "🔍 Checking GitHub Actions runner service status..."
        SERVICE_STATUS=$(sshpass -p "$SSH_PASS" ssh $SSH_OPTIONS $SSH_USER@$SSH_HOST "cd ~/actions-runner && ./svc.sh status" 2>/dev/null || echo "FAILED")
        if echo "$SERVICE_STATUS" | grep -q "Started\|Running"; then
          echo "✅ Runner service is already running"
        else
          echo "⚠️ Runner service not running, starting runner directly..."
          # Start runner directly in background
          sshpass -p "$SSH_PASS" ssh $SSH_OPTIONS $SSH_USER@$SSH_HOST "cd ~/actions-runner && nohup ./run.sh > runner.log 2>&1 &"
          sleep 15
        fi
        echo "✅ Wake-up and runner configuration completed"
        echo "runner-status=wake-completed" >> $GITHUB_OUTPUT
  build-watch-app:
    name: Watch App Build (Scaleway Mac)
    runs-on: [self-hosted, macos]
    needs: [setup, check-runner-availability, wake-scaleway-runner]
    timeout-minutes: 40
    env:
      # Project Configuration - defined at job level for proper variable resolution
      PROJECT_NAME: "DrMuscleWatchApp.xcodeproj"
      SCHEME_NAME: "DrMuscleWatchApp-iOS"
      APP_NAME: "DrMuscleWatchApp"
      PROJECT_PATH: "DrMuscleWatch/v1/DrMuscleWatchApp"

      # Bundle Identifiers (matching Apple Developer Portal configuration)
      IOS_STUB_BUNDLE_ID: "com.drmaxmuscle.max"
      WATCH_APP_BUNDLE_ID: "com.drmaxmuscle.max.watchkitapp"

      # Provisioning Profile Names (must match names in Apple Developer Portal)
      WATCH_APP_PROVISIONING_PROFILE_NAME: "Dr_Muscle_Watch_App_Store_Profile"
      IOS_STUB_PROVISIONING_PROFILE_NAME: "Dr Muscle June 11 26"
    outputs:
      watch-start-time: ${{ steps.watch-start-time.outputs.watch-start-time }}
      build-start-time: ${{ steps.build_watch.outputs.build-start-time }}
      build-end-time: ${{ steps.build_watch.outputs.build-end-time }}
      build-duration: ${{ steps.build_watch.outputs.build-duration }}
      build-success: ${{ steps.build_watch.outputs.build-success }}
      ipa-found: ${{ steps.check_ipa.outputs.ipa-found }}
      testflight-upload-success: ${{ steps.upload_testflight.outputs.upload-success }}
      version-code: ${{ needs.setup.outputs.version-code }}

    steps:
    - name: WORKFLOW VERSION CHECK - This confirms our changes are active
      run: |
        echo "🔍 WORKFLOW VERSION CHECK - v1.1 with SDK installation fixes"
        echo "This step confirms that our updated workflow is running"
        echo "Branch: ${{ github.ref }}"
        echo "Commit: ${{ github.sha }}"
        echo "Workflow file should include Pre-SDK and SDK installation steps"
        echo "✅ Workflow version check completed"
    - name: Record Watch app job start time
      id: watch-start-time
      run: |
        WATCH_START_TIME=$(date +%s)
        echo "watch-start-time=$WATCH_START_TIME" >> $GITHUB_OUTPUT
        echo "Watch app build started at: $(date -r $WATCH_START_TIME)"
    - uses: actions/checkout@v4

    - name: Debug repository structure after checkout
      run: |
        echo "🔍 Debugging repository structure after checkout..."
        echo "Current working directory: $(pwd)"
        echo ""
        echo "Root directory contents:"
        ls -la
        echo ""
        echo "DrMuscleWatch directory structure (if exists):"
        if [ -d "DrMuscleWatch" ]; then
          find DrMuscleWatch -type f -name "*.xcodeproj" -o -name "Info.plist" | head -20
          echo ""
          echo "DrMuscleWatch directory tree (first 3 levels):"
          find DrMuscleWatch -maxdepth 3 -type d | sort
        else
          echo "❌ DrMuscleWatch directory does not exist!"
        fi
        echo ""
        echo "Looking for any .xcodeproj files in repository:"
        find . -name "*.xcodeproj" -type d | head -10
    - name: Validate Watch app project structure
      run: |
        echo "🔍 Validating Dr. Muscle Watch app project structure..."
        # Check if the project directory exists
        if [ ! -d "$PROJECT_PATH" ]; then
          echo "❌ Project directory not found: $PROJECT_PATH"
          exit 1
        fi
        # Check if the Xcode project exists (xcodeproj is a directory bundle)
        if [ ! -d "$PROJECT_PATH/$PROJECT_NAME" ]; then
          echo "❌ Xcode project not found: $PROJECT_PATH/$PROJECT_NAME"
          exit 1
        fi
        echo "✅ Watch app project structure validated successfully"
        echo "  Project: $PROJECT_PATH/$PROJECT_NAME"
        echo "  Scheme: $SCHEME_NAME"
        echo "  Info.plist: Generated during build (GENERATE_INFOPLIST_FILE = YES)"
    - name: Set App Version in Build Settings
      run: |
        # Use version from setup job
        CLEAN_VERSION="${{ needs.setup.outputs.version-code }}"
        echo "🔧 Setting Watch app version: $CLEAN_VERSION"
        echo "ℹ️ Version will be set via build settings since GENERATE_INFOPLIST_FILE = YES"
        echo "ℹ️ The Info.plist will be generated during build with version: $CLEAN_VERSION"
        # The version will be set via MARKETING_VERSION and CURRENT_PROJECT_VERSION in the build command
        echo "✅ Version configuration ready for build process"
    - name: Clean up existing keychain
      run: |
        # Remove existing signing keychain if it exists (for self-hosted runners)
        if security list-keychains | grep -q "signing_temp.keychain"; then
          security delete-keychain signing_temp.keychain
          echo "🧹 Removed existing signing_temp.keychain"
        else
          echo "✅ No existing signing keychain to clean up"
        fi
        # Also check for keychain files in the filesystem
        if [ -f "$HOME/Library/Keychains/signing_temp.keychain-db" ]; then
          rm -f "$HOME/Library/Keychains/signing_temp.keychain-db"
          echo "🧹 Removed signing_temp.keychain-db file"
        fi
        if [ -f "$HOME/Library/Keychains/signing_temp.keychain" ]; then
          rm -f "$HOME/Library/Keychains/signing_temp.keychain"
          echo "🧹 Removed signing_temp.keychain file"
        fi
    - name: Install Apple Intermediate Certificate
      run: |
        # Download and install Apple Intermediate Certificate Authority for complete certificate chain
        echo "📥 Downloading Apple Intermediate Certificate Authority..."
        curl -o AppleWWDRCAG3.cer "https://www.apple.com/certificateauthority/AppleWWDRCAG3.cer"
        # Import intermediate certificate into login keychain (no sudo required)
        echo "🔧 Importing intermediate certificate into login keychain..."
        if security import AppleWWDRCAG3.cer -k login.keychain -T /usr/bin/codesign; then
          echo "✅ Apple Intermediate Certificate Authority imported successfully"
        else
          echo "ℹ️ Certificate already exists in keychain (this is fine)"
        fi
        echo "✅ Apple Intermediate Certificate Authority is available in login keychain"
    - name: Import Code-Signing Certificates
      run: |
        echo "🔐 Importing code signing certificates..."
        # Create temporary keychain
        KEYCHAIN_NAME="signing_temp.keychain"
        KEYCHAIN_PASSWORD="temp_password"
        security create-keychain -p "$KEYCHAIN_PASSWORD" "$KEYCHAIN_NAME"
        security set-keychain-settings -lut 21600 "$KEYCHAIN_NAME"
        security list-keychains -d user -s "$KEYCHAIN_NAME" login.keychain
        # Import certificate
        echo "${{ secrets.P12_CERTIFICATE }}" | base64 --decode > certificate.p12
        security import certificate.p12 -P "${{ secrets.P12_CERTIFICATE_PASSWORD }}" -A -t cert -f pkcs12 -k "$KEYCHAIN_NAME"
        security set-key-partition-list -S apple-tool:,apple:,codesign: -s -k "$KEYCHAIN_PASSWORD" "$KEYCHAIN_NAME"
        # Clean up
        rm certificate.p12
        echo "✅ Certificate import completed"
    - name: Verify certificate import
      run: |
        echo "🔍 Verifying imported certificates for watchOS..."
        echo "📋 All certificates in keychain:"
        security find-identity -v
        echo ""
        echo "🔐 Code signing certificates specifically:"
        security find-identity -v -p codesigning
        echo ""
        echo "🔍 Checking if certificate import succeeded..."
        CERT_COUNT=$(security find-identity -v -p codesigning | grep -c "Apple Distribution" || echo "0")
        if [ "$CERT_COUNT" = "0" ]; then
          echo "❌ No code signing certificates found! Certificate import may have failed."
          security find-identity -v
          exit 1
        else
          echo "✅ Found code signing certificates in keychain"
        fi
    - name: Install Provisioning Profiles and Get Names
      id: install-profiles
      run: |
        echo "📝 Installing provisioning profiles for iOS and watchOS targets..."
        mkdir -p "$HOME/Library/MobileDevice/Provisioning Profiles"
        # Install iOS provisioning profile (for the companion app)
        if [ -n "${{ secrets.IOS_PROVISIONING_PROFILE_BASE64 }}" ]; then
          IOS_PROFILE_PATH="$HOME/Library/MobileDevice/Provisioning Profiles/ios_profile.mobileprovision"
          echo "${{ secrets.IOS_PROVISIONING_PROFILE_BASE64 }}" | base64 --decode > "$IOS_PROFILE_PATH"
          IOS_PROFILE_NAME=$(/usr/libexec/PlistBuddy -c 'Print :Name' /dev/stdin <<< $(security cms -D -i "$IOS_PROFILE_PATH"))
          echo "IOS_STUB_PROVISIONING_PROFILE_NAME=$IOS_PROFILE_NAME" >> $GITHUB_ENV
          echo "✅ iOS Profile Name: $IOS_PROFILE_NAME"
        else
          echo "❌ IOS_PROVISIONING_PROFILE_BASE64 secret not found"
          exit 1
        fi
        # Install watchOS provisioning profile
        if [ -n "${{ secrets.WATCH_APP_PROVISIONING_PROFILE_BASE64 }}" ]; then
          WATCH_PROFILE_PATH="$HOME/Library/MobileDevice/Provisioning Profiles/watch_profile.mobileprovision"
          echo "${{ secrets.WATCH_APP_PROVISIONING_PROFILE_BASE64 }}" | base64 --decode > "$WATCH_PROFILE_PATH"
          WATCH_PROFILE_NAME=$(/usr/libexec/PlistBuddy -c 'Print :Name' /dev/stdin <<< $(security cms -D -i "$WATCH_PROFILE_PATH"))
          echo "WATCH_APP_PROVISIONING_PROFILE_NAME=$WATCH_PROFILE_NAME" >> $GITHUB_ENV
          echo "✅ watchOS Profile Name: $WATCH_PROFILE_NAME"
        else
          echo "⚠️ WATCH_APP_PROVISIONING_PROFILE_BASE64 secret not found, using iOS profile for both"
          echo "WATCH_APP_PROVISIONING_PROFILE_NAME=$IOS_PROFILE_NAME" >> $GITHUB_ENV
        fi
    - name: Validate Apple Team ID
      id: validate-team-id
      run: |
        # Check if APPLE_TEAM_ID secret is set and not empty
        if [ -z "${{ secrets.APPLE_TEAM_ID }}" ]; then
          echo "❌ APPLE_TEAM_ID secret is not set or empty"
          echo "ℹ️ Using fallback team ID from MAUI workflow: 7AAXZ47995"
          echo "apple-team-id=7AAXZ47995" >> $GITHUB_OUTPUT
        else
          echo "✅ APPLE_TEAM_ID secret is configured"
          echo "apple-team-id=${{ secrets.APPLE_TEAM_ID }}" >> $GITHUB_OUTPUT
        fi
    - name: Setup Provisioning Profiles
      id: setup-provisioning
      run: |
        # Both targets use the same bundle ID for standalone watchOS app
        echo "📝 Both iOS stub and watchOS targets use the same bundle ID (com.drmaxmuscle.max)"
        echo "📝 Shared provisioning profile installed for both targets"
        # List installed profiles for debugging
        echo "📋 Installed provisioning profiles:"
        ls -la ~/Library/MobileDevice/Provisioning\ Profiles/ || echo "No profiles directory found"
    - name: Pre-SDK Installation Diagnostics
      run: |
        echo "🔍 PRE-SDK DIAGNOSTICS - This step should always run"
        echo "Current working directory: $(pwd)"
        echo "Environment variables:"
        echo "  PROJECT_PATH: $PROJECT_PATH"
        echo "  PROJECT_NAME: $PROJECT_NAME"
        echo "  SCHEME_NAME: $SCHEME_NAME"
        echo "User: $(whoami)"
        echo "Date: $(date)"
        echo "✅ Pre-SDK diagnostics completed"
    - name: Install watchOS SDK and Simulators
      run: |
        echo "🔄 SDK INSTALLATION STEP STARTED"
        echo "Xcode version: $(xcodebuild -version)"
        echo "Xcode path: $(xcode-select -p)"
        echo "📋 Currently installed SDKs:"
        xcodebuild -showsdks

        # Debug environment variables
        echo "🔍 DEBUG: Environment variables:"
        echo "  PROJECT_PATH: '$PROJECT_PATH'"
        echo "  PROJECT_NAME: '$PROJECT_NAME'"
        echo "  SCHEME_NAME: '$SCHEME_NAME'"
        echo "  Current directory: $(pwd)"

        # Check if project directory exists
        echo "🔍 Checking project structure..."
        if [ ! -d "$PROJECT_PATH" ]; then
          echo "❌ Project directory does not exist: $PROJECT_PATH"
          echo "📁 Available directories in DrMuscleWatch:"
          ls -la DrMuscleWatch/ || echo "DrMuscleWatch directory not found"
          exit 1
        fi
        echo "✅ Project directory exists: $PROJECT_PATH"

        # Check if project file exists
        if [ ! -d "$PROJECT_PATH/$PROJECT_NAME" ]; then
          echo "❌ Xcode project file does not exist: $PROJECT_PATH/$PROJECT_NAME"
          echo "📁 Contents of project directory:"
          ls -la "$PROJECT_PATH/" || echo "Cannot list project directory"
          exit 1
        fi
        echo "✅ Xcode project file exists: $PROJECT_PATH/$PROJECT_NAME"

        # List available schemes
        echo "🔍 Listing available schemes in project..."
        cd "$PROJECT_PATH"
        xcodebuild -list -project "${PROJECT_NAME}" || echo "❌ Failed to list schemes"

        # Check if destinations work for our project, which is a more reliable check
        echo "🔍 Checking if watchOS destination is available for the project..."
        DEST_OUTPUT=$(xcodebuild -showdestinations -project "${PROJECT_NAME}" -scheme "${SCHEME_NAME}" 2>&1)
        DEST_EXIT_CODE=$?
        cd -

        echo "🔍 xcodebuild exit code: $DEST_EXIT_CODE"
        echo "🔍 xcodebuild output:"
        echo "$DEST_OUTPUT"

        # A common error message if the SDK is missing or not registered
        if echo "$DEST_OUTPUT" | grep -q "is not installed"; then
          echo "⚠️ watchOS SDK appears to be missing or not registered. Attempting installation..."

          echo "🔄 Downloading watchOS platform..."
          xcodebuild -downloadPlatform watchOS || echo "🚨 Platform download failed, but continuing build attempt."
          echo "🔄 Running Xcode first launch to register components..."
          xcodebuild -runFirstLaunch || echo "🚨 First launch failed, but continuing build attempt."
          echo "📋 Verifying SDKs again after installation attempt:"
          xcodebuild -showsdks | grep -i watchos || echo "❌ Still no watchOS SDKs found."
        else
          echo "✅ watchOS SDK and destination appear to be correctly installed."
        fi
        echo "🔍 Checking simulator runtimes:"
        xcrun simctl list runtimes | grep -i watchos || echo "No watchOS runtimes found"
        echo "🔍 Checking available simulators:"
        xcrun simctl list devices | grep -i watch || echo "No Watch simulators found"
        echo "✅ SDK installation step completed"
    - name: Create ExportOptions.plist
      run: |
        # Change to project directory to create ExportOptions.plist where it will be used
        cd "$PROJECT_PATH"
        echo "📝 Creating ExportOptions.plist for manual signing"
        cat << EOF > ExportOptions.plist
        <!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
        <plist version="1.0">
        <dict>
            <key>method</key>
            <string>app-store</string>
            <key>teamID</key>
            <string>${{ steps.validate-team-id.outputs.apple-team-id }}</string>
            <key>uploadSymbols</key>
            <true/>
            <key>signingStyle</key>
            <string>manual</string>
            <key>signingCertificate</key>
            <string>Apple Distribution</string>
            <key>provisioningProfiles</key>
            <dict>
                <key>${{ env.IOS_STUB_BUNDLE_ID }}</key>
                <string>${{ env.IOS_STUB_PROVISIONING_PROFILE_NAME }}</string>
                <key>${{ env.WATCH_APP_BUNDLE_ID }}</key>
                <string>${{ env.WATCH_APP_PROVISIONING_PROFILE_NAME }}</string>
            </dict>
        </dict>
        </plist>
        EOF
        echo "✅ ExportOptions.plist created for iOS and watchOS targets"
        cat ExportOptions.plist
    - name: Validate Certificates and Provisioning Profiles
      id: validate-signing
      run: |
        echo "🔍 COMPREHENSIVE CERTIFICATE AND PROFILE VALIDATION"
        echo "This step validates all signing components before building"
        # Initialize validation status
        VALIDATION_PASSED=true
        echo ""
        echo "📋 1. CERTIFICATE VALIDATION"
        echo "================================"
        # Check for Apple Distribution certificates
        echo "🔍 Checking for Apple Distribution certificates..."
        DIST_CERTS=$(security find-identity -v -p codesigning | grep "Apple Distribution" || echo "")
        if [ -n "$DIST_CERTS" ]; then
          echo "✅ Apple Distribution certificates found:"
          echo "$DIST_CERTS"
          # Verify the specific certificate for our team
          TEAM_CERT=$(echo "$DIST_CERTS" | grep "Dr. Muscle (7AAXZ47995)" || echo "")
          if [ -n "$TEAM_CERT" ]; then
            echo "✅ Team-specific certificate found: Dr. Muscle (7AAXZ47995)"
          else
            echo "⚠️ Team-specific certificate not found, but other Distribution certificates available"
          fi
        else
          echo "❌ No Apple Distribution certificates found!"
          VALIDATION_PASSED=false
        fi
        # Check for Apple Development certificates (backup)
        echo ""
        echo "🔍 Checking for Apple Development certificates (backup)..."
        DEV_CERTS=$(security find-identity -v -p codesigning | grep "Apple Development" || echo "")
        if [ -n "$DEV_CERTS" ]; then
          echo "✅ Apple Development certificates found (can be used as fallback):"
          echo "$DEV_CERTS"
        else
          echo "⚠️ No Apple Development certificates found"
        fi
        # Check for Apple Intermediate Certificate
        echo ""
        echo "🔍 Checking for Apple Intermediate Certificate..."
        INTERMEDIATE_CERT=$(security find-certificate -c "Apple Worldwide Developer Relations Certification Authority" login.keychain 2>/dev/null || echo "")
        if [ -n "$INTERMEDIATE_CERT" ]; then
          echo "✅ Apple Intermediate Certificate found in keychain"
        else
          echo "⚠️ Apple Intermediate Certificate not found (may cause signing issues)"
        fi
        echo ""
        echo "📋 2. PROVISIONING PROFILE VALIDATION"
        echo "====================================="
        # Check provisioning profiles directory
        PROFILES_DIR="$HOME/Library/MobileDevice/Provisioning Profiles"
        if [ -d "$PROFILES_DIR" ]; then
          PROFILE_COUNT=$(ls -1 "$PROFILES_DIR"/*.mobileprovision 2>/dev/null | wc -l)
          echo "✅ Provisioning profiles directory exists with $PROFILE_COUNT profile(s)"
          if [ $PROFILE_COUNT -gt 0 ]; then
            echo "📋 Installed profiles:"
            ls -la "$PROFILES_DIR"/*.mobileprovision
            # Validate the shared profile used by both targets
            SHARED_PROFILE="$PROFILES_DIR/shared_profile.mobileprovision"
            if [ -f "$SHARED_PROFILE" ]; then
              echo ""
              echo "🔍 Validating shared app profile..."
              # Extract and validate profile content
              if security cms -D -i "$SHARED_PROFILE" > /tmp/shared_profile_check.plist 2>/dev/null; then
                if plutil -lint /tmp/shared_profile_check.plist >/dev/null 2>&1; then
                  PROFILE_NAME=$(plutil -extract Name raw /tmp/shared_profile_check.plist 2>/dev/null || echo "Unknown")
                  BUNDLE_ID=$(plutil -extract Entitlements.application-identifier raw /tmp/shared_profile_check.plist 2>/dev/null | sed 's/^[^.]*\.//' || echo "Unknown")
                  TEAM_ID=$(plutil -extract TeamIdentifier.0 raw /tmp/shared_profile_check.plist 2>/dev/null || echo "Unknown")
                  PROFILE_UUID=$(plutil -extract UUID raw /tmp/shared_profile_check.plist 2>/dev/null || echo "Unknown")
                  EXPIRY_DATE=$(plutil -extract ExpirationDate raw /tmp/shared_profile_check.plist 2>/dev/null || echo "Unknown")
                  echo "  ✅ Profile Name: $PROFILE_NAME"
                  echo "  ✅ Bundle ID: $BUNDLE_ID"
                  echo "  ✅ Team ID: $TEAM_ID"
                  echo "  ✅ Profile UUID: $PROFILE_UUID"
                  echo "  ✅ Expiry Date: $EXPIRY_DATE"
                  # Validate bundle ID (informational - let Xcode handle final validation)
                  if [[ "$BUNDLE_ID" == "${{ env.WATCH_APP_BUNDLE_ID }}" ]]; then
                    echo "  ✅ Bundle ID matches watchOS target: ${{ env.WATCH_APP_BUNDLE_ID }}"
                  elif [[ "$BUNDLE_ID" == "${{ env.IOS_STUB_BUNDLE_ID }}" ]]; then
                    echo "  ✅ Bundle ID matches iOS target: ${{ env.IOS_STUB_BUNDLE_ID }}"
                  elif [[ "$BUNDLE_ID" == "com.drmaxmuscle.max.watchkitapp" ]]; then
                    echo "  ✅ Bundle ID is watchOS format: com.drmaxmuscle.max.watchkitapp"
                  else
                    echo "  ⚠️ Bundle ID info: Expected iOS (${{ env.IOS_STUB_BUNDLE_ID }}) or watchOS (${{ env.WATCH_APP_BUNDLE_ID }}), Got: $BUNDLE_ID"
                    echo "  ℹ️ Xcode will validate during build process"
                  fi
                  # Validate team ID matches
                  if [[ "$TEAM_ID" == "7AAXZ47995" ]]; then
                    echo "  ✅ Team ID matches expected: 7AAXZ47995"
                  else
                    echo "  ❌ Team ID mismatch! Expected: 7AAXZ47995, Got: $TEAM_ID"
                    VALIDATION_PASSED=false
                  fi
                  # Check if profile is expired
                  if [ "$EXPIRY_DATE" != "Unknown" ]; then
                    CURRENT_DATE=$(date +%s)
                    EXPIRY_TIMESTAMP=$(date -j -f "%Y-%m-%dT%H:%M:%SZ" "$EXPIRY_DATE" +%s 2>/dev/null || echo "0")
                    if [ $EXPIRY_TIMESTAMP -gt $CURRENT_DATE ]; then
                      DAYS_UNTIL_EXPIRY=$(( (EXPIRY_TIMESTAMP - CURRENT_DATE) / 86400 ))
                      echo "  ✅ Profile is valid (expires in $DAYS_UNTIL_EXPIRY days)"
                    else
                      echo "  ❌ Profile has expired!"
                      VALIDATION_PASSED=false
                    fi
                  fi
                else
                  echo "  ❌ Profile plist is invalid"
                  VALIDATION_PASSED=false
                fi
                rm -f /tmp/watch_profile_check.plist
              else
                echo "  ❌ Failed to extract profile content"
                VALIDATION_PASSED=false
              fi
            else
              echo "❌ watchOS profile not found at: $WATCH_PROFILE"
              VALIDATION_PASSED=false
            fi
          else
            echo "❌ No provisioning profiles installed!"
            VALIDATION_PASSED=false
          fi
        else
          echo "❌ Provisioning profiles directory does not exist!"
          VALIDATION_PASSED=false
        fi
        echo ""
        echo "📋 3. SIGNING CONFIGURATION VALIDATION"
        echo "======================================"
        # Validate team ID configuration
        TEAM_ID="${{ steps.validate-team-id.outputs.apple-team-id }}"
        echo "🔍 Team ID from configuration: $TEAM_ID"
        if [[ "$TEAM_ID" == "7AAXZ47995" ]]; then
          echo "✅ Team ID configuration is correct"
        else
          echo "❌ Team ID configuration is incorrect! Expected: 7AAXZ47995, Got: $TEAM_ID"
          VALIDATION_PASSED=false
        fi
        # Validate bundle ID configuration
        echo "🔍 watchOS Bundle ID from configuration: ${{ env.WATCH_APP_BUNDLE_ID }}"
        echo "🔍 iOS Bundle ID from configuration: ${{ env.IOS_STUB_BUNDLE_ID }}"
        if [[ "${{ env.WATCH_APP_BUNDLE_ID }}" == "com.drmaxmuscle.max.watchkitapp" ]] && [[ "${{ env.IOS_STUB_BUNDLE_ID }}" == "com.drmaxmuscle.max" ]]; then
          echo "✅ Bundle ID configuration is correct"
        else
          echo "⚠️ Bundle ID configuration may need review"
          echo "  Expected watchOS: com.drmaxmuscle.max.watchkitapp"
          echo "  Expected iOS: com.drmaxmuscle.max"
          # Don't fail validation for this - let Xcode handle it
        fi
        echo ""
        echo "📋 4. FINAL VALIDATION RESULT"
        echo "============================="
        if [ "$VALIDATION_PASSED" = "true" ]; then
          echo "✅ ALL VALIDATION CHECKS PASSED"
          echo "🎯 Ready to proceed with building and signing"
          echo "validation-passed=true" >> $GITHUB_OUTPUT
        else
          echo "⚠️ VALIDATION WARNINGS DETECTED"
          echo "🔄 Proceeding with build - Xcode will handle final signing validation"
          echo "validation-passed=false" >> $GITHUB_OUTPUT
          echo ""
          echo "🔧 VALIDATION WARNINGS (informational only):"
          echo "1. Some bundle ID or profile mismatches detected"
          echo "2. Xcode will attempt to resolve signing issues during build"
          echo "3. If build fails, check provisioning profiles and certificates"
          echo "4. Ensure profiles match bundle IDs and team ID: 7AAXZ47995"
          echo "5. Check that provisioning profiles have not expired"
          echo ""
          echo "🎯 Continuing with build process..."
        fi
    - name: Clean up duplicate files and build Watch App
      id: build_watch
      run: |
        # Check validation result before proceeding
        if [ "${{ steps.validate-signing.outputs.validation-passed }}" = "true" ]; then
          echo "✅ Certificate and profile validation passed - proceeding with build"
        else
          echo "⚠️ Validation warnings detected - proceeding with build anyway"
          echo "🔄 Xcode will handle final signing validation during build process"
        fi
        # Record build start time
        BUILD_START_TIME=$(date +%s)
        echo "build-start-time=$BUILD_START_TIME" >> $GITHUB_OUTPUT
        echo "Watch app build compilation started at: $(date -r $BUILD_START_TIME)"
        # Change to project directory
        cd "$PROJECT_PATH"
        echo "🧹 Cleaning up duplicate files that may cause build conflicts..."
        # Remove duplicate files at root level (keep only the ones in DrMuscleWatchApp subdirectory)
        if [ -f "ContentView.swift" ]; then
          rm "ContentView.swift"
          echo "Removed duplicate ContentView.swift at root level"
        fi
        if [ -f "DrMuscleWatchApp.swift" ]; then
          rm "DrMuscleWatchApp.swift"
          echo "Removed duplicate DrMuscleWatchApp.swift at root level"
        fi
        echo "🧹 Cleaning Xcode build cache and derived data..."
        # Clean any existing build artifacts that might cause conflicts
        rm -rf build/
        rm -rf ~/Library/Developer/Xcode/DerivedData/DrMuscleWatchApp-*
        # Clean the project to ensure fresh build (Grok's recommendation)
        echo "🧹 Performing comprehensive clean..."
        rm -rf build/
        rm -rf ~/Library/Developer/Xcode/DerivedData/DrMuscleWatchApp-*
        echo "🧹 Cleaning Xcode project..."
        xcodebuild clean -project "${PROJECT_NAME}" -scheme "${SCHEME_NAME}" -configuration Release || echo "⚠️ Clean command failed, continuing..."
        echo "🎯 Starting Watch app build for simulator (no signing required)..."
        echo "  Project: $PROJECT_NAME"
        echo "  Scheme: $SCHEME_NAME"
        echo "  Configuration: Release"
        # Create build directory
        mkdir -p build
        # Get Team ID for signing
        APPLE_TEAM_ID="${{ steps.validate-team-id.outputs.apple-team-id }}"
        echo "📝 Team ID: $APPLE_TEAM_ID"
        # Build for device to create archive for TestFlight using manual signing.
        # We archive the iOS stub scheme, which embeds the standalone watch app.
        echo "📝 Using manual signing with Apple Distribution certificate"
        CODE_SIGN_STYLE="Manual"
        CODESIGN_IDENTITY="Apple Distribution: Dr. Muscle ($APPLE_TEAM_ID)"
        TEAM_SETTING="DEVELOPMENT_TEAM=$APPLE_TEAM_ID"
        # Clean up any old archives and build artifacts first
        echo "🧹 Cleaning up old archives and build artifacts..."
        rm -rf build/*.xcarchive
        rm -rf build/
        # Clean Xcode build cache to ensure fresh build with updated settings
        echo "🧹 Cleaning Xcode build cache..."
        xcodebuild clean -project "${PROJECT_NAME}" -scheme "${SCHEME_NAME}"
        # Verify project configuration before building (Grok's recommendation)
        echo "🔍 Verifying project configuration..."
        echo "📋 iOS Stub Target Build Settings:"
        xcodebuild -project "${PROJECT_NAME}" -target "DrMuscleWatchApp-iOS" -showBuildSettings | grep -E "(PRODUCT_BUNDLE_IDENTIFIER|SKIP_INSTALL|SUPPORTED_PLATFORMS)" || echo "iOS stub settings check failed"
        echo ""
        echo "📋 watchOS Target Build Settings:"
        xcodebuild -project "${PROJECT_NAME}" -target "DrMuscleWatchApp" -showBuildSettings | grep -E "(PRODUCT_BUNDLE_IDENTIFIER|SKIP_INSTALL|SUPPORTED_PLATFORMS|TARGETED_DEVICE_FAMILY|INFOPLIST_KEY_LSApplicationLaunchProhibited|INFOPLIST_KEY_WKWatchOnly|INFOPLIST_KEY_CFBundleSupportedPlatforms|INFOPLIST_KEY_UIDeviceFamily)" || echo "watchOS settings check failed"
        # Check for manual Info.plist files that might override bundle ID (Grok's recommendation)
        echo ""
        echo "🔍 Checking for manual Info.plist files..."
        find . -name "*Info.plist" -not -path "./build/*" -not -path "./.git/*" | while read plist_file; do
          echo "Found: $plist_file"
          if [ -f "$plist_file" ]; then
            BUNDLE_ID=$(plutil -extract CFBundleIdentifier raw "$plist_file" 2>/dev/null || echo "No bundle ID")
            echo "  Bundle ID: $BUNDLE_ID"
          fi
        done
        echo "🎯 Building independent watchOS app archive..."
          CLEAN_VERSION="${{ needs.setup.outputs.version-code }}"
          if xcodebuild archive \
            -project "${PROJECT_NAME}" \
            -scheme "${SCHEME_NAME}" \
            -configuration Release \
            -destination "generic/platform=iOS" \
            -archivePath "build/${APP_NAME}.xcarchive" \
            CODE_SIGN_IDENTITY="$CODESIGN_IDENTITY" \
            $TEAM_SETTING \
            COMPILER_INDEX_STORE_ENABLE=NO \
            MARKETING_VERSION="$CLEAN_VERSION" \
            CURRENT_PROJECT_VERSION="$CLEAN_VERSION" 2>&1; then
            echo "✅ Archive successful with manual signing"
          else
            echo "❌ Archive failed"
            exit 1
          fi
        # Record build end time and calculate duration
        BUILD_END_TIME=$(date +%s)
        echo "build-end-time=$BUILD_END_TIME" >> $GITHUB_OUTPUT
        BUILD_DURATION=$((BUILD_END_TIME - BUILD_START_TIME))
        echo "build-duration=$BUILD_DURATION" >> $GITHUB_OUTPUT
        echo "Watch app build compilation completed at: $(date -r $BUILD_END_TIME)"
        echo "Build duration: $(($BUILD_DURATION / 60))m $(($BUILD_DURATION % 60))s"
        # Mark build as successful
        echo "build-success=true" >> $GITHUB_OUTPUT
        echo "✅ Build completed successfully"
    - name: Prepare for TestFlight Upload
      id: check_ipa
      run: |
        # Change to project directory
        cd "$PROJECT_PATH"
        echo "📦 Preparing watchOS archive for TestFlight upload..."
        # Check if archive exists
        if [ ! -d "build/${APP_NAME}.xcarchive" ]; then
          echo "❌ Archive not found: build/${APP_NAME}.xcarchive"
          exit 1
        fi
        echo "✅ Archive found: build/${APP_NAME}.xcarchive"
        echo "📝 Exporting IPA from archive for TestFlight upload..."
        # Validate ExportOptions.plist before export
        echo "🔍 Printing ExportOptions.plist before export:"
        pwd
        ls -la
        if [ -f ExportOptions.plist ]; then
          cat ExportOptions.plist
          plutil -lint ExportOptions.plist || { echo '❌ ExportOptions.plist is invalid!'; exit 1; }
        else
          echo '❌ ExportOptions.plist not found!'; exit 1;
        fi
        # Export IPA from archive
        echo "🔄 Exporting IPA for TestFlight..."
        if xcodebuild -exportArchive \
          -archivePath "build/${APP_NAME}.xcarchive" \
          -exportPath "build/export" \
          -exportOptionsPlist ExportOptions.plist; then
          echo "🔍 Checking exported files..."
          echo "📁 Contents of build/export/:"
          ls -la build/export/ || echo "Export directory not found"
          # Find the actual IPA file (more robust detection)
          IPA_FILE=$(find build/export -name "*.ipa" | head -1)
          if [ -n "$IPA_FILE" ] && [ -f "$IPA_FILE" ]; then
            echo "✅ IPA export successful: $IPA_FILE"
            # Set outputs for upload step
            echo "ipa-found=true" >> $GITHUB_OUTPUT
            echo "ipa-path=$IPA_FILE" >> $GITHUB_OUTPUT
            # Get IPA size for info
            IPA_SIZE=$(du -sh "$IPA_FILE" | cut -f1)
            echo "📦 IPA Size: ${IPA_SIZE}"
          else
            echo "❌ No IPA file found after export"
            echo "🔍 Expected: build/export/${APP_NAME}.ipa"
            echo "🔍 Looking for any .ipa files in build/export/:"
            find build/export -name "*.ipa" -ls || echo "No .ipa files found"
            exit 1
          fi
        else
          echo "❌ IPA export failed"
          exit 1
        fi
        # Inspect archive structure to understand platform detection issue
        echo "🔍 Detailed archive inspection for platform detection..."
        find "build/${APP_NAME}.xcarchive" -name "*.app" -type d | while read app_path; do
          echo "Found app bundle: $app_path"
          if [ -f "$app_path/Info.plist" ]; then
            BUNDLE_ID=$(plutil -extract CFBundleIdentifier raw "$app_path/Info.plist" 2>/dev/null || echo "Unknown")
            PLATFORMS=$(plutil -extract CFBundleSupportedPlatforms raw "$app_path/Info.plist" 2>/dev/null || echo "Unknown")
            DEVICE_FAMILY=$(plutil -extract UIDeviceFamily raw "$app_path/Info.plist" 2>/dev/null || echo "Unknown")
            echo "  Bundle ID: $BUNDLE_ID"
            echo "  Supported Platforms: $PLATFORMS"
            echo "  Device Family: $DEVICE_FAMILY"
          fi
        done
        # Check archive Info.plist
        echo "🔍 Archive Info.plist inspection..."
        ARCHIVE_INFO="build/${APP_NAME}.xcarchive/Info.plist"
        if [ -f "$ARCHIVE_INFO" ]; then
          echo "Archive Info.plist key sections:"
          plutil -extract ApplicationProperties raw "$ARCHIVE_INFO" 2>/dev/null || echo "No ApplicationProperties"
          plutil -extract ArchiveVersion raw "$ARCHIVE_INFO" 2>/dev/null || echo "No ArchiveVersion"
        else
          echo "❌ Archive Info.plist missing!"
        fi
    - name: Verify Standalone watchOS App Configuration
      run: |
        # Change to project directory
        cd "$PROJECT_PATH"
        echo "🔍 Verifying standalone watchOS app configuration in archive..."
        ARCHIVE_PATH="build/${APP_NAME}.xcarchive"
        if [ -d "$ARCHIVE_PATH" ]; then
          # Find the iOS app bundle first
          IOS_APP_BUNDLE=$(find "$ARCHIVE_PATH/Products/Applications" -name "*.app" -type d -maxdepth 1 | head -1)
          
          if [ -n "$IOS_APP_BUNDLE" ] && [ -d "$IOS_APP_BUNDLE" ]; then
            echo "✅ Found iOS app bundle: $IOS_APP_BUNDLE"
            
            # Look for embedded watchOS app inside the iOS app
            WATCH_APP_BUNDLE=$(find "$IOS_APP_BUNDLE/Watch" -name "*.app" -type d -maxdepth 1 2>/dev/null | head -1)
            
            if [ -n "$WATCH_APP_BUNDLE" ] && [ -d "$WATCH_APP_BUNDLE" ]; then
              echo "✅ Found embedded watchOS app bundle: $WATCH_APP_BUNDLE"
              # Check watchOS app Info.plist for required keys
              WATCH_INFO_PLIST="$WATCH_APP_BUNDLE/Info.plist"
              if [ -f "$WATCH_INFO_PLIST" ]; then
                echo "✅ Found watchOS app Info.plist"
                echo "🔍 Checking required independent watchOS app keys..."
                # Check WKRunsIndependentlyOfCompanionApp (key for independent watchOS apps)
                WK_RUNS_INDEPENDENTLY=$(plutil -extract WKRunsIndependentlyOfCompanionApp raw "$WATCH_INFO_PLIST" 2>/dev/null || echo "missing")
                echo "  WKRunsIndependentlyOfCompanionApp: $WK_RUNS_INDEPENDENTLY"
                # Check LSApplicationLaunchProhibited
                LS_LAUNCH_PROHIBITED=$(plutil -extract LSApplicationLaunchProhibited raw "$WATCH_INFO_PLIST" 2>/dev/null || echo "missing")
                echo "  LSApplicationLaunchProhibited: $LS_LAUNCH_PROHIBITED"
                # Check CFBundleSupportedPlatforms
                SUPPORTED_PLATFORMS=$(plutil -extract CFBundleSupportedPlatforms raw "$WATCH_INFO_PLIST" 2>/dev/null || echo "missing")
                echo "  CFBundleSupportedPlatforms: $SUPPORTED_PLATFORMS"
                # Check UIDeviceFamily
                DEVICE_FAMILY=$(plutil -extract UIDeviceFamily raw "$WATCH_INFO_PLIST" 2>/dev/null || echo "missing")
                echo "  UIDeviceFamily: $DEVICE_FAMILY"
                # Check bundle identifier
                WATCH_BUNDLE_ID=$(plutil -extract CFBundleIdentifier raw "$WATCH_INFO_PLIST" 2>/dev/null || echo "missing")
                echo "  watchOS CFBundleIdentifier: $WATCH_BUNDLE_ID"
                # Validate independent companion watchOS app configuration
                # Note: WKWatchOnly should NOT be true for independent companion apps
                if [[ "$WK_RUNS_INDEPENDENTLY" == "true" ]] && [[ "$LS_LAUNCH_PROHIBITED" == "true" ]] && [[ "$SUPPORTED_PLATFORMS" == *"WatchOS"* ]] && [[ "$DEVICE_FAMILY" == *"4"* ]]; then
                  echo "✅ Independent companion watchOS app configuration is correct"
                  echo "🎯 Archive should be recognized by App Store Connect as independent companion watchOS app"
                else
                  echo "⚠️ Independent companion watchOS app configuration may be incomplete"
                  echo "  Expected: WKRunsIndependentlyOfCompanionApp=true, LSApplicationLaunchProhibited=true, CFBundleSupportedPlatforms=WatchOS, UIDeviceFamily=4"
                  echo "  Note: WKWatchOnly should NOT be set for independent companion apps"
                  echo "  This might cause issues with App Store Connect recognition"
                fi
              else
                echo "❌ watchOS app Info.plist not found at: $WATCH_INFO_PLIST"
              fi
            else
              echo "❌ Embedded watchOS app bundle not found in iOS app"
              echo "🔍 Looking for Watch directory in iOS app:"
              ls -la "$IOS_APP_BUNDLE/" | grep -i watch || echo "No Watch directory found"
            fi
          else
            echo "❌ iOS app bundle not found in archive"
            echo "🔍 Contents of archive Applications directory:"
            ls -la "$ARCHIVE_PATH/Products/Applications/" || echo "Cannot list archive contents"
          fi
        else
          echo "❌ Archive not found: $ARCHIVE_PATH"
        fi
    - name: Verify Archive for Upload
      id: check_ipa_real
      run: |
        # Change to project directory
        cd "$PROJECT_PATH"
        # Get archive path from previous step
        ARCHIVE_PATH="${{ steps.check_ipa.outputs.ipa-path }}"
        if [ -n "$ARCHIVE_PATH" ] && [ -f "$ARCHIVE_PATH" ]; then
          echo "✅ IPA verified for upload: $ARCHIVE_PATH"
          echo "ipa-found=true" >> $GITHUB_OUTPUT
          echo "ipa-path=$ARCHIVE_PATH" >> $GITHUB_OUTPUT
          # Get IPA info
          IPA_SIZE=$(du -sh "$ARCHIVE_PATH" | cut -f1)
          echo "📦 Final IPA Size: ${IPA_SIZE}"
        else
          echo "❌ IPA is missing or invalid!"
          echo "ipa-found=false" >> $GITHUB_OUTPUT
          exit 1
        fi
    - name: Install App Store Connect API Key
      run: |
        mkdir -p ~/private_keys
        echo -n "${{ secrets.APPSTORE_API_PRIVATE_KEY }}" | base64 --decode --output ~/private_keys/AuthKey_${{ secrets.APPSTORE_API_KEY_ID }}.p8
        echo "✅ App Store Connect API Key installed."
    - name: Upload to TestFlight
      id: upload_testflight
      env:
        API_KEY: ${{ secrets.APPSTORE_API_KEY_ID }}
        API_ISSUER: ${{ secrets.APPSTORE_ISSUER_ID }}
      run: |
        # Change to project directory
        cd "$PROJECT_PATH"
        ARCHIVE_PATH="${{ steps.check_ipa_real.outputs.ipa-path }}"
        echo "🚀 Uploading Watch app archive to TestFlight..."
        echo "  Archive: $ARCHIVE_PATH"
        # Detailed IPA inspection before upload
        echo ""
        echo "🔍 DETAILED IPA INSPECTION FOR UPLOAD DEBUGGING"
        echo "=============================================="
        # Check IPA structure
        echo "📁 IPA structure:"
        if [[ "$ARCHIVE_PATH" == *.ipa ]]; then
          unzip -l "$ARCHIVE_PATH" | head -20
          echo ""
          echo "📱 Checking for Payload directory:"
          unzip -l "$ARCHIVE_PATH" | grep -E "Payload/" | head -5
        else
          find "$ARCHIVE_PATH" -type f -name "*.app" -o -name "*.plist" | head -20
        fi
        # Check main app bundle (extract from IPA if needed)
        if [[ "$ARCHIVE_PATH" == *.ipa ]]; then
          # Extract IPA to temporary directory for inspection
          TEMP_DIR=$(mktemp -d)
          unzip -q "$ARCHIVE_PATH" -d "$TEMP_DIR"
          MAIN_APP=$(find "$TEMP_DIR/Payload" -name "*.app" -type d | head -1)
        else
          MAIN_APP=$(find "$ARCHIVE_PATH/Products/Applications" -name "*.app" -type d | head -1)
        fi
        if [ -n "$MAIN_APP" ]; then
          echo ""
          echo "📱 Main app bundle: $MAIN_APP"
          if [ -f "$MAIN_APP/Info.plist" ]; then
            echo "📋 Main app Info.plist contents:"
            echo "🔍 Bundle Identifier:"
            plutil -extract CFBundleIdentifier raw "$MAIN_APP/Info.plist" 2>/dev/null || echo "  ❌ CFBundleIdentifier missing"
            echo "🔍 Supported Platforms:"
            plutil -extract CFBundleSupportedPlatforms raw "$MAIN_APP/Info.plist" 2>/dev/null || echo "  ❌ CFBundleSupportedPlatforms missing"
            echo "🔍 Device Family:"
            plutil -extract UIDeviceFamily raw "$MAIN_APP/Info.plist" 2>/dev/null || echo "  ❌ UIDeviceFamily missing"
            echo "🔍 WKWatchOnly:"
            plutil -extract WKWatchOnly raw "$MAIN_APP/Info.plist" 2>/dev/null || echo "  ❌ WKWatchOnly missing"
            echo "🔍 LSApplicationLaunchProhibited:"
            plutil -extract LSApplicationLaunchProhibited raw "$MAIN_APP/Info.plist" 2>/dev/null || echo "  ❌ LSApplicationLaunchProhibited missing"
          fi
          # Check for embedded watchOS app
          WATCH_APP=$(find "$MAIN_APP" -name "*.app" -type d | grep -v "$(basename "$MAIN_APP")" | head -1)
          if [ -n "$WATCH_APP" ]; then
            echo ""
            echo "⌚ Embedded watchOS app: $WATCH_APP"
            if [ -f "$WATCH_APP/Info.plist" ]; then
              echo "📋 watchOS app Info.plist:"
              plutil -extract CFBundleIdentifier raw "$WATCH_APP/Info.plist" 2>/dev/null || echo "  ❌ watchOS CFBundleIdentifier missing"
            fi
          else
            echo "⚠️ No embedded watchOS app found in main bundle"
          fi
        fi
        # Cleanup temporary directory if created
        if [[ "$ARCHIVE_PATH" == *.ipa ]] && [ -n "$TEMP_DIR" ]; then
          rm -rf "$TEMP_DIR"
        fi
        # Check archive Info.plist
        ARCHIVE_INFO="$ARCHIVE_PATH/Info.plist"
        if [ -f "$ARCHIVE_INFO" ]; then
          echo ""
          echo "📋 Archive Info.plist contents:"
          plutil -p "$ARCHIVE_INFO" | grep -E "(ApplicationProperties|ArchiveVersion|CreationDate|Name|SchemeName)"
        fi
        # Use xcrun altool to upload archive directly to TestFlight
        # Upload as iOS app containing embedded independent watchOS app
        echo ""
        echo "🚀 Starting altool upload as iOS app (with verbose output)..."
        if xcrun altool --upload-app \
          -f "$ARCHIVE_PATH" \
          -t ios \
          --apiKey "$API_KEY" \
          --apiIssuer "$API_ISSUER" \
          --verbose; then
          echo "upload-success=true" >> $GITHUB_OUTPUT
          echo "✅ Successfully uploaded to TestFlight as independent watchOS app"
        else
          echo "upload-success=false" >> $GITHUB_OUTPUT
          echo "❌ Failed to upload to TestFlight"
          echo ""
          echo "🔍 UPLOAD FAILURE ANALYSIS:"
          echo "- Upload failed - check archive structure and App Store Connect configuration"
          echo "- Ensure archive contains valid independent watchOS app"
          echo "- Verify provisioning profiles and certificates are correct"
          exit 1
        fi
    # Output Watch app build summary
    - name: Output Watch App Build Summary
      if: always()
      run: |
        echo "### ⌚ Dr. Muscle Watch App Build Summary" >> $GITHUB_STEP_SUMMARY
        echo "- **Project:** $PROJECT_PATH/$PROJECT_NAME" >> $GITHUB_STEP_SUMMARY
        echo "- **Scheme:** $SCHEME_NAME" >> $GITHUB_STEP_SUMMARY
        echo "- **Bundle ID:** $WATCH_APP_BUNDLE_ID" >> $GITHUB_STEP_SUMMARY
        # Add performance metrics
        if [ -n "${{ steps.build_watch.outputs.build-duration }}" ]; then
          BUILD_DURATION="${{ steps.build_watch.outputs.build-duration }}"
          echo "- **Build Duration:** $(($BUILD_DURATION / 60))m $(($BUILD_DURATION % 60))s" >> $GITHUB_STEP_SUMMARY
        fi
        # Add build status information
        if [ "${{ steps.build_watch.outputs.build-success }}" = "true" ]; then
          echo "- **Build:** ✅ Completed successfully" >> $GITHUB_STEP_SUMMARY
        else
          echo "- **Build:** ❌ Failed" >> $GITHUB_STEP_SUMMARY
        fi
        # Add package information if found
        if [ "${{ steps.check_ipa.outputs.ipa-found }}" = "true" ]; then
          echo "- **Package:** ✅ Built successfully" >> $GITHUB_STEP_SUMMARY
          echo "- **Package Path:** \`${{ steps.check_ipa.outputs.ipa-path }}\`" >> $GITHUB_STEP_SUMMARY
          echo "- **Version:** ${{ needs.setup.outputs.version-name }}" >> $GITHUB_STEP_SUMMARY
          echo "- **watchOS Target:** watchOS 9.0+" >> $GITHUB_STEP_SUMMARY
          # Add deployment information
          if [ "${{ steps.upload_testflight.outputs.upload-success }}" = "true" ]; then
            echo "- **Deployment:** ✅ Uploaded to TestFlight" >> $GITHUB_STEP_SUMMARY
            echo "- **Distribution:** TestFlight (Internal Testing)" >> $GITHUB_STEP_SUMMARY
          else
            echo "- **Deployment:** ⚠️ Not uploaded to TestFlight" >> $GITHUB_STEP_SUMMARY
            echo "  - ℹ️ Reason: Upload failed or credentials missing" >> $GITHUB_STEP_SUMMARY
          fi
        else
          echo "- **Package:** ❌ Build failed, no package was generated" >> $GITHUB_STEP_SUMMARY
        fi
        # Add workflow timing
        if [ -n "${{ needs.setup.outputs.start-time }}" ] && [ -n "${{ steps.watch-start-time.outputs.watch-start-time }}" ]; then
          START_TIME="${{ needs.setup.outputs.start-time }}"
          CURRENT_TIME=$(date +%s)
          TOTAL_DURATION=$((CURRENT_TIME - START_TIME))
          echo "- **Total Workflow Runtime:** $(($TOTAL_DURATION / 60))m $(($TOTAL_DURATION % 60))s" >> $GITHUB_STEP_SUMMARY
        fi