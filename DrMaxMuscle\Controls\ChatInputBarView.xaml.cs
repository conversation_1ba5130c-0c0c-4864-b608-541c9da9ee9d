
//using Microsoft.Maui.Controls.PlatformConfiguration;
//using Microsoft.Maui.Controls.PlatformConfiguration.AndroidSpecific;
using DrMaxMuscle.Dependencies;
using Microsoft.Maui.Networking;

namespace DrMaxMuscle.Controls;

public partial class ChatInputBarView : ContentView
{
    public event EventHandler Tapped;

    public ChatInputBarView()
    {
        InitializeComponent();
        chatTextInput.BindingContext = this;

        RefreshLocalized();
        MessagingCenter.Subscribe<Message.LanguageChangeMessage>(this, "LocalizeUpdated", (obj) => {
            RefreshLocalized();
        });
    }
    void RefreshLocalized()
    {
        //BtnSend.Text = AppResources.Send;
    }
    //public void Handle_Completed(object sender, EventArgs e)
    //{
    //if (Tapped != null)
    //{
    //    Tapped.Invoke(this, e);
    //}
    //chatTextInput.Focus();
    //}

    public async void UnFocusEntry()
    {
        try
        {
            DependencyService.Get<IKeyboardHelper>().HideKeyboard();
        }
        catch (Exception ex)
        {

        }
        chatTextInput?.Unfocus();
    }

    void Handle_Completed(object sender, System.EventArgs e)
    {
        if (Tapped != null)
        {
            Tapped.Invoke(this, e);
        }
        if (Connectivity.NetworkAccess == NetworkAccess.Internet && chatTextInput != null)
            chatTextInput.Text = "";
        //chatTextInput.Focus();
    }

    public static readonly BindableProperty MessageTextProperty = BindableProperty.Create("MessageText", typeof(string), typeof(ChatInputBarView), string.Empty, BindingMode.TwoWay, null, (bindable, oldValue, newValue) =>
    {
        ((ChatInputBarView)bindable).SetMessageText();
    });

    private void SetMessageText()
    {
        this.MessageText = chatTextInput.Text;
        frmSendMessage.IsEnabled = MessageText.Length > 0;
    }

    public string MessageText
    {
        get { return (string)GetValue(MessageTextProperty); }

        set
        {
            SetValue(MessageTextProperty, value);
        }
    }

    void chatTextInput_TextChanged(System.Object sender, TextChangedEventArgs e)
    {
        try
        {
            if (chatTextInput != null && !string.IsNullOrEmpty(chatTextInput.Text))
            {
                if (chatTextInput.Text.Length == 1)
                    chatTextInput.Text = char.ToUpper(chatTextInput.Text[0]) + "";
                else if (chatTextInput.Text.Length > 1)
                    chatTextInput.Text = char.ToUpper(chatTextInput.Text[0]) + chatTextInput.Text.Substring(1);
            }
        }
        catch (Exception ex)
        {
            
        }
    }

    public void chatTextInput_Focused(object sender, FocusEventArgs e)
    {
        var screenHeight = App.ScreenHeight;
        if (Device.RuntimePlatform == Device.Android)
        {
            // Code specific to Android platform
            // uncomment code please
            //App.Current.On<Android>().UseWindowSoftInputModeAdjust(WindowSoftInputModeAdjust.Resize);

        }
        else if (Device.RuntimePlatform == Device.iOS)
        {
            // Code specific to iOS platform
            if (screenHeight <= 736)
            {
                frameGrid.Margin = new Thickness(10, 10, 10, 10);
            }
            else
                frameGrid.Margin = new Thickness(10, 10, 10, -20);


        }



    }

    public void chatTextInput_Unfocused(object sender, FocusEventArgs e)
    {
        if (Device.RuntimePlatform == Device.Android)
        {
            // Code specific to Android platform
            // uncomment code please
            //App.Current.On<Android>().UseWindowSoftInputModeAdjust(WindowSoftInputModeAdjust.Pan);
        }
        else if (Device.RuntimePlatform == Device.iOS)
        {
            // Code specific to iOS platform
            frameGrid.Margin = new Thickness(10, 10, 10, 20);
        }

    }
}
