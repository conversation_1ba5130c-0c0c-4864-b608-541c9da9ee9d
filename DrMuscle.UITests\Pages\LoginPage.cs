using OpenQA.Selenium;
using OpenQA.Selenium.Appium;
using OpenQA.Selenium.Support.UI;
using System;

namespace DrMuscle.UITests.Pages
{
    public class LoginPage
    {
        private readonly AppiumDriver _driver;
        private readonly WebDriverWait _wait;
        
        // Element locators
        private AppiumElement? EmailField => FindElement(MobileBy.AccessibilityId("EmailEntry")) 
            ?? FindElement(By.XPath("//XCUIElementTypeTextField[contains(@value,'email')]"));
            
        private AppiumElement? PasswordField => FindElement(MobileBy.AccessibilityId("PasswordEntry"))
            ?? FindElement(By.XPath("//XCUIElementTypeSecureTextField[contains(@value,'password')]"));
            
        private AppiumElement? LoginButton => FindElement(MobileBy.AccessibilityId("LoginButton"))
            ?? FindElement(By.XPath("//XCUIElementTypeStaticText[@name='Log in']"));
            
        private AppiumElement? CreateAccountButton => FindElement(MobileBy.AccessibilityId("GetStartedButton"))
            ?? FindElement(By.XPath("//XCUIElementTypeStaticText[@name='Create new account']"));
            
        private AppiumElement? GoogleLoginButton => FindElement(MobileBy.AccessibilityId("LoginWithGoogleButton"));
        
        private AppiumElement? AppleLoginButton => FindElement(MobileBy.AccessibilityId("LoginWithAppleButton"));
        
        public LoginPage(AppiumDriver driver)
        {
            _driver = driver;
            _wait = new WebDriverWait(_driver, TimeSpan.FromSeconds(10));
        }
        
        public void Login(string email, string password)
        {
            EnterEmail(email);
            EnterPassword(password);
            TapLogin();
        }
        
        public void EnterEmail(string email)
        {
            var field = EmailField;
            field?.Clear();
            field?.SendKeys(email);
        }
        
        public void EnterPassword(string password)
        {
            var field = PasswordField;
            field?.Clear();
            field?.SendKeys(password);
        }
        
        public void TapLogin()
        {
            LoginButton?.Click();
        }
        
        public void TapCreateAccount()
        {
            CreateAccountButton?.Click();
        }
        
        public void TapGoogleLogin()
        {
            GoogleLoginButton?.Click();
        }
        
        public void TapAppleLogin()
        {
            AppleLoginButton?.Click();
        }
        
        public bool IsDisplayed()
        {
            try
            {
                return EmailField != null && EmailField.Displayed;
            }
            catch
            {
                return false;
            }
        }
        
        public void WaitForPageToLoad()
        {
            _wait.Until(d => EmailField != null && EmailField.Displayed);
        }
        
        private AppiumElement? FindElement(By by)
        {
            try
            {
                return _driver.FindElement(by);
            }
            catch (NoSuchElementException)
            {
                return null;
            }
        }
    }
}