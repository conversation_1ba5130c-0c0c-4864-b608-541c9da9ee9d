import XCTest
import AuthenticationServices
@testable import DrMuscleWatchApp

class LoginViewModelTests: XCTestCase {
    var sut: LoginViewModel!
    
    override func setUp() {
        super.setUp()
        sut = LoginViewModel()
        // Clear any existing auth state
        AuthenticationManager.shared.clearAuthState()
    }
    
    override func tearDown() {
        sut = nil
        AuthenticationManager.shared.clearAuthState()
        super.tearDown()
    }
    
    // MARK: - Initial State Tests
    
    func testLoginViewModelInitialState() {
        // Given a new login view model
        // When checking initial state
        // Then all properties should have default values
        XCTAssertFalse(sut.isAuthenticating)
        XCTAssertNil(sut.errorMessage)
    }
    
    // MARK: - Sign in with Apple Tests
    
    func testStartSignInWithAppleSetsAuthenticatingState() {
        // Given the login view model
        // When starting Sign in with Apple
        sut.startSignInWithApple()
        
        // Then it should set authenticating state
        XCTAssertTrue(sut.isAuthenticating)
        XCTAssertNil(sut.errorMessage)
    }
    
    func testAuthenticationStateMonitoring() async throws {
        // Given the login view model
        // When authentication starts
        sut.startSignInWithApple()
        
        // Then it should be authenticating
        XCTAssertTrue(sut.isAuthenticating)
        
        // Wait a bit for the monitoring to kick in
        try await Task.sleep(nanoseconds: 200_000_000) // 0.2 seconds
        
        // When the auth manager reports an error
        await MainActor.run {
            AuthenticationManager.shared.authError = "Test error"
        }
        
        // Wait for the view model to pick up the change
        try await Task.sleep(nanoseconds: 600_000_000) // 0.6 seconds
        
        // Then the view model should reflect the error
        await MainActor.run {
            XCTAssertFalse(self.sut.isAuthenticating)
            XCTAssertEqual(self.sut.errorMessage, "Test error")
        }
    }
    
    func testAuthenticationSuccessStopsAuthenticating() async throws {
        // Given the login view model
        // When authentication starts
        sut.startSignInWithApple()
        
        // Then it should be authenticating
        XCTAssertTrue(sut.isAuthenticating)
        
        // Wait a bit for the monitoring to kick in
        try await Task.sleep(nanoseconds: 200_000_000) // 0.2 seconds
        
        // When the auth manager reports success
        let userInfo = UserInfosModel(
            id: "123",
            email: "<EMAIL>",
            token: "test-token",
            firstName: "Test",
            lastName: "User"
        )
        
        await MainActor.run {
            AuthenticationManager.shared.storeAuthState(userInfo: userInfo)
        }
        
        // Wait for the view model to pick up the change
        try await Task.sleep(nanoseconds: 600_000_000) // 0.6 seconds
        
        // Then the view model should stop authenticating
        await MainActor.run {
            XCTAssertFalse(self.sut.isAuthenticating)
            XCTAssertNil(self.sut.errorMessage)
        }
    }
    
    func testStartSignInWithAppleClearsErrorMessage() {
        // Given the view model with an existing error
        sut.errorMessage = "Previous error"
        
        // When starting Sign in with Apple
        sut.startSignInWithApple()
        
        // Then it should clear the error message
        XCTAssertNil(sut.errorMessage)
    }
    
    func testMultipleSignInAttemptsHandledCorrectly() {
        // Given the login view model
        // When starting Sign in with Apple multiple times
        sut.startSignInWithApple()
        let firstIsAuthenticating = sut.isAuthenticating
        
        sut.startSignInWithApple()
        let secondIsAuthenticating = sut.isAuthenticating
        
        // Then both should set authenticating state
        XCTAssertTrue(firstIsAuthenticating)
        XCTAssertTrue(secondIsAuthenticating)
    }
    
    func testViewModelDoesNotRetainAuthorizationController() async throws {
        // Given the login view model
        // When starting and completing authentication
        sut.startSignInWithApple()
        
        // Simulate authentication completion
        await MainActor.run {
            AuthenticationManager.shared.authError = "Cancelled"
        }
        
        // Wait for cleanup
        try await Task.sleep(nanoseconds: 1_000_000_000) // 1 second
        
        // Then the view model should not retain the authorization controller
        // (We can't directly test this, but we can verify the view model is in a clean state)
        await MainActor.run {
            XCTAssertFalse(self.sut.isAuthenticating)
        }
    }
    
    func testShouldRemoveQuickSignInMethod() {
        // Given the login view model
        // When checking for quickSignIn method
        // Then it should not exist (as we're removing mock login)
        let mirror = Mirror(reflecting: sut)
        let hasQuickSignIn = mirror.children.contains { $0.label == "quickSignIn" }
        XCTAssertFalse(hasQuickSignIn)
    }
} 