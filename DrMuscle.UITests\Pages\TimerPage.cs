using OpenQA.Selenium;
using OpenQA.Selenium.Appium;
using OpenQA.Selenium.Support.UI;
using System;
using System.Linq;

namespace DrMuscle.UITests.Pages
{
    /// <summary>
    /// Page object for Timer functionality
    /// </summary>
    public class TimerPage
    {
        private readonly AppiumDriver? _driver;
        private readonly WebDriverWait _wait;
        
        public TimerPage(AppiumDriver driver)
        {
            _driver = driver ?? throw new ArgumentNullException(nameof(driver));
            _wait = new WebDriverWait(_driver, TimeSpan.FromSeconds(10));
        }
        
        // Timer display elements
        private AppiumElement? TimerDisplay => FindElement(MobileBy.AccessibilityId("RestTimerDisplay"));
        private AppiumElement? TimerCountdown => FindElement(MobileBy.AccessibilityId("TimerCountdown"));
        private AppiumElement? TimerProgressBar => FindElement(MobileBy.AccessibilityId("TimerProgress"));
        
        // Timer control elements
        private AppiumElement? SkipTimerButton => FindElement(MobileBy.AccessibilityId("SkipTimer"));
        private AppiumElement? StartTimerButton => FindElement(MobileBy.AccessibilityId("StartTimer"));
        private AppiumElement? PauseTimerButton => FindElement(MobileBy.AccessibilityId("PauseTimer"));
        private AppiumElement? ResumeTimerButton => FindElement(MobileBy.AccessibilityId("ResumeTimer"));
        
        // Notification elements
        private AppiumElement? TimerNotification => FindElement(MobileBy.AccessibilityId("TimerNotification"));
        private AppiumElement? WarningNotification => FindElement(MobileBy.AccessibilityId("TimerWarning"));
        private AppiumElement? CompletionAlert => FindElement(MobileBy.AccessibilityId("TimerCompleteAlert"));
        private AppiumElement? SkipConfirmation => FindElement(MobileBy.AccessibilityId("TimerSkipConfirmation"));
        
        // Timer state indicators
        private AppiumElement? TimerStateLabel => FindElement(MobileBy.AccessibilityId("TimerState"));
        private AppiumElement? TimerTypeLabel => FindElement(MobileBy.AccessibilityId("TimerType"));
        
        // Actions
        public void SkipTimer()
        {
            SkipTimerButton?.Click();
        }
        
        public void StartTimer()
        {
            StartTimerButton?.Click();
        }
        
        public void PauseTimer()
        {
            PauseTimerButton?.Click();
        }
        
        public void ResumeTimer()
        {
            ResumeTimerButton?.Click();
        }
        
        public void DismissNotification()
        {
            TimerNotification?.Click();
        }
        
        // Verifications
        public bool IsTimerRunning()
        {
            // Check multiple indicators of timer running
            if (TimerDisplay != null && TimerDisplay.Displayed)
            {
                var timerText = GetRemainingTime();
                if (!string.IsNullOrEmpty(timerText) && timerText != "0:00")
                {
                    return true;
                }
            }
            
            // Check if skip button is visible (only visible when timer is running)
            if (SkipTimerButton != null && SkipTimerButton.Displayed)
            {
                return true;
            }
            
            // Check timer state
            var state = TimerStateLabel?.Text;
            if (state != null && state.Contains("Running"))
            {
                return true;
            }
            
            return false;
        }
        
        public bool IsTimerPaused()
        {
            return ResumeTimerButton != null && ResumeTimerButton.Displayed;
        }
        
        public bool IsStartTimerButtonVisible()
        {
            return StartTimerButton != null && StartTimerButton.Displayed;
        }
        
        public bool IsSkipButtonVisible()
        {
            return SkipTimerButton != null && SkipTimerButton.Displayed;
        }
        
        // Data retrieval
        public string GetRemainingTime()
        {
            // Try multiple selectors
            var timeText = TimerCountdown?.Text ?? TimerDisplay?.Text ?? "";
            
            // Clean up the text (remove "Rest:" prefix if present)
            timeText = timeText.Replace("Rest:", "").Replace("Rest", "").Trim();
            
            // If still empty, try finding by partial text
            if (string.IsNullOrEmpty(timeText))
            {
                var timerElement = FindElement(MobileBy.XPath("//XCUIElementTypeStaticText[contains(@name, ':')]"));
                if (timerElement != null)
                {
                    timeText = timerElement.Text;
                }
            }
            
            return timeText;
        }
        
        public string GetTimerNotification()
        {
            return TimerNotification?.Text ?? CompletionAlert?.Text ?? "";
        }
        
        public string GetWarningNotification()
        {
            return WarningNotification?.Text ?? "";
        }
        
        public string GetSkipConfirmation()
        {
            return SkipConfirmation?.Text ?? "";
        }
        
        public string GetTimerType()
        {
            return TimerTypeLabel?.Text ?? "";
        }
        
        public double GetProgressPercentage()
        {
            var progressValue = TimerProgressBar?.GetAttribute("value");
            if (!string.IsNullOrEmpty(progressValue))
            {
                if (double.TryParse(progressValue, out double progress))
                {
                    return progress;
                }
            }
            
            return 0;
        }
        
        // Wait methods
        public bool WaitForTimerToStart(int timeoutSeconds = 5)
        {
            try
            {
                var shortWait = new WebDriverWait(_driver!, TimeSpan.FromSeconds(timeoutSeconds));
                shortWait.Until(d => IsTimerRunning());
                return true;
            }
            catch (WebDriverTimeoutException)
            {
                return false;
            }
        }
        
        public bool WaitForTimerToComplete(int maxWaitSeconds = 300)
        {
            try
            {
                var longWait = new WebDriverWait(_driver!, TimeSpan.FromSeconds(maxWaitSeconds));
                longWait.Until(d => !IsTimerRunning());
                return true;
            }
            catch (WebDriverTimeoutException)
            {
                return false;
            }
        }
        
        // Helper methods
        private AppiumElement? FindElement(By by)
        {
            try
            {
                return _driver?.FindElement(by) as AppiumElement;
            }
            catch (NoSuchElementException)
            {
                return null;
            }
        }
        
        // Alternative selectors for different app implementations
        public bool IsTimerVisible()
        {
            // Try multiple ways to detect timer
            var timerElements = new[]
            {
                TimerDisplay,
                TimerCountdown,
                FindElement(MobileBy.XPath("//XCUIElementTypeStaticText[contains(@name, 'Rest')]")),
                FindElement(MobileBy.XPath("//XCUIElementTypeStaticText[contains(@name, 'Timer')]")),
                FindElement(MobileBy.ClassName("TimerView"))
            };
            
            return timerElements.Any(elem => elem != null && elem.Displayed);
        }
    }
}