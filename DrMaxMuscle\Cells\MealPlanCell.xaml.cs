using DrMaxMuscle.Controls;
using DrMaxMuscle.Helpers;
using RGPopup.Maui.Services;
using System.Text;
using System.Text.RegularExpressions;

namespace DrMaxMuscle.Cells;

public partial class MealPlanCell : ContentView
{
    bool IsGPT4Changed = false;
    bool isAnimating = false;
    public MealPlanCell()
    {
        InitializeComponent();
        FrmContainer.Opacity = 0;
        FrmContainer.Opacity = 0;
    }
    protected override async void OnBindingContextChanged()
    {
        base.OnBindingContextChanged();
        infoFrame.IsVisible = false;
        AdjustingImage.IsVisible = true;
        if (BindingContext is BotModel viewModel)
        {
            // Access the Question property before binding
            string initialQuestionValue = viewModel.Question;
            Console.WriteLine("Started =\n" + initialQuestionValue + "\nEnded");
            IsGPT4Changed = (!string.IsNullOrEmpty(viewModel.Part2) && viewModel.Part2 == "true") ? true : false;

            //editBubbleImg.IsVisible = !(viewModel.SetsImage == "true") ? false : true;
            //editBubbleImg.IsVisible = (!string.IsNullOrEmpty(viewModel.SetsImage) && viewModel.SetsImage == "true") ? true : false;
            FrmContainer.Padding = (!string.IsNullOrEmpty(viewModel.SetsImage) && viewModel.SetsImage == "true") ? new Thickness(20, 0, 0, -7) : new Thickness(20, 0, 20, -7);
            //FrmContainer.Padding = (!string.IsNullOrEmpty(viewModel.SetsImage) && viewModel.SetsImage == "true") ? new Thickness(20, 0, 20, -7) : new Thickness(20, 0, 20, -7);

            if (viewModel.Part3 == "false")
            {
                if (DeviceInfo.Platform == DevicePlatform.Android)
                    FormatTextForGPT4(initialQuestionValue);
                else
                    FormatLabelText(initialQuestionValue);
            }
            else
            {
                FormatTextForGPT4(initialQuestionValue);
            }
            if (viewModel.IsAdjusting == false)
            {
                // Stop the animation
                column1.Width = new GridLength(0.5, GridUnitType.Star);
                column2.Width = new GridLength(0.5, GridUnitType.Star);
                adjustingLabel.IsVisible = true;
                if (AdjustingImage.IsAnimationPlaying == true)
                    AdjustingImage.IsAnimationPlaying = false;

                AdjustingImage.HeightRequest = 18;
                AdjustingImage.WidthRequest = 18;
                //arrowImg.Margin = new Thickness(0, -7, 0, 0);
                //arrowImg.WidthRequest = 25;
                //arrowImg.HeightRequest = 25;
                adjustingLabel.Text = "(Adjusted)";
                //if (initialQuestionValue.ToLower().Contains("protein shake"))
                //{
                //    arrowImg.WidthRequest = 22;
                //    arrowImg.HeightRequest = 22;
                //}
            }
            else if (viewModel.IsAdjusting == true)
            {
                // Start the animation
                MainThread.BeginInvokeOnMainThread(() => {
                    adjustingLabel.IsVisible = true;
                    adjustingLabel.Text = "(Adjusting...)";
                    AdjustingImage.HeightRequest = 18;
                    AdjustingImage.WidthRequest = 18;
                    AdjustingImage.IsAnimationPlaying = true;
                    column1.Width = new GridLength(0.5, GridUnitType.Star);
                    column2.Width = new GridLength(0.5, GridUnitType.Star);
                });


                //arrowImg.Margin = new Thickness(0, 2, 0, 0);
                //arrowImg.WidthRequest = 16;
                //arrowImg.HeightRequest = 16;
                //if (initialQuestionValue.ToLower().Contains("protein shake"))
                //{
                //    arrowImg.WidthRequest = 15;
                //    arrowImg.HeightRequest = 15;
                //}

            }
            else
            {
                MainThread.BeginInvokeOnMainThread(() => {

                    adjustingLabel.IsVisible = false;
                    AdjustingImage.IsVisible = false;
                    if (initialQuestionValue.ToLower().Contains("final") && initialQuestionValue.ToLower().Contains("meal plan"))
                    {
                        infoFrame.IsVisible = true;
                    }
                    else
                    {
                        infoFrame.IsVisible = false;
                    }
                    column1.Width = new GridLength(0.8, GridUnitType.Star);
                    column2.Width = new GridLength(0.2, GridUnitType.Star);
                });
            }
            //LblAnswer.FormattedText = FormatLabelText(initialQuestionValue);
        }
        await FrmContainer.FadeTo(1, 500, Easing.CubicInOut);
        //await LblAnswer.FadeTo(1, 500);

    }

    private async void FormatTextForGPT4(string initialQuestionValue)
    {
        string pattern = @"\)([A-Z])";
        string replacement = ")\n$1"; // $1 represents the capital letter matched

        initialQuestionValue = Regex.Replace(initialQuestionValue, pattern, replacement);

        initialQuestionValue = initialQuestionValue.Replace("[", "");
        initialQuestionValue = initialQuestionValue.Replace("]", "");
        initialQuestionValue = initialQuestionValue.Replace("Separate", "");
        initialQuestionValue = Regex.Replace(initialQuestionValue, @"(^.*Stop here.*(\r?\n)?)", "", RegexOptions.Multiline);
        initialQuestionValue = Regex.Replace(initialQuestionValue, @"(^.*stop here.*(\r?\n)?)", "", RegexOptions.Multiline);

        //initialQuestionValue = initialQuestionValue.Replace("Stop here", "");
        //initialQuestionValue = initialQuestionValue.Replace("stop here", "");
        initialQuestionValue = initialQuestionValue.TrimEnd();
        Console.WriteLine("Cell Value = " + initialQuestionValue);
        FormattedString formattedString = new FormattedString();
        LblHeader.IsVisible = false;
        string[] lines = IsGPT4Changed ? initialQuestionValue.Split(new[] { Environment.NewLine }, StringSplitOptions.None) : initialQuestionValue.Split(new[] { Environment.NewLine }, StringSplitOptions.RemoveEmptyEntries);
        //string[] lines = initialQuestionValue.Split(new[] { Environment.NewLine }, StringSplitOptions.RemoveEmptyEntries);
        //StringBuilder formattedString = new StringBuilder();
        bool skipNextLine = false;
        var lastLine = lines.LastOrDefault();
        foreach (string line in lines)
        {
            var data = line;
            data = data.Replace("...", "");
            //data = data.Replace("[", "");
            //data = data.Replace("]", "");
            //data = data.Replace("Stop here", "");
            //data = data.Replace("stop here", "");

            //if (skipNextLine)
            //{
            //    skipNextLine = false;
            //    continue; // Skip the current line
            //}

            if (data.Contains("Breakfast:") || data.Contains("Lunch:") || data.Contains("Dinner:") || data.ToLower().Contains("shake:") || data.ToLower().Contains("snack 1:") || data.ToLower().Contains("snack 2:") || data.ToLower().Contains("shopping") || data.ToLower().Contains("meal plan:"))
            {
                if (!data.ToLower().Contains("shake:"))
                    skipNextLine = true;
                else
                {
                    data = GetProteinShakeHeader(data);
                    if (data.ToLower().Contains("shake:"))
                        data = "Protein Shake";


                }
                if (data.ToLower().Contains("meal plan:"))
                    data = data.Replace("meal plan:", "meal plan");
                LblHeader.IsVisible = true;
                LblHeader.Text = data;
                //Span headerSpan = new Span
                //{
                //    Text = data + "\n",
                //    FontAttributes = FontAttributes.Bold,
                //    FontSize = 17
                //};

                //// Add the span to the formatted string
                //formattedString.Spans.Add(headerSpan);

                //formattedString.AppendLine(data);
                continue; // Skip the next line
            }
            else
            {
                Span regularSpan = new Span
                {
                    Text = (lastLine.ToLower().ToString() == data.ToLower().ToString()) ? data : data + "\n",
                    FontSize = 17,
                    LineHeight = DeviceInfo.Platform == DevicePlatform.Android ? 1.3 : 1.2
                };
                formattedString.Spans.Add(regularSpan);
                //formattedString.AppendLine(data);
            }
        }
        //var finalData = formattedString.ToString().TrimEnd();
        //finalData += "\n";

        // Remove multiple consecutive blank lines while preserving formatting
        //    formattedString = RemoveExtraBlankLines(formattedString);
        LblAnswer.FormattedText = formattedString;
        FrmContainer.Padding = (editBubbleImg.IsVisible == true || (initialQuestionValue.ToLower().Contains("final") && initialQuestionValue.ToLower().Contains("meal plan"))) ? new Thickness(20, 0, 0, 15) : new Thickness(20, 0, 20, 15);
        //FrmContainer.Padding = (editBubbleImg.IsVisible == true || (initialQuestionValue.ToLower().Contains("final") && initialQuestionValue.ToLower().Contains("meal plan"))) ? new Thickness(20, 0, 20, 15) : new Thickness(20, 0, 20, 15);
        //LblAnswer.Text = formattedString.ToString().TrimEnd();
    }
    private FormattedString RemoveExtraBlankLines(FormattedString formattedText)
    {
        // Convert FormattedString to string
        string text = formattedText.ToString();

        // Define the regex pattern to match multiple consecutive blank lines
        string pattern = @"(\r?\n){2,}";

        // Replace multiple consecutive blank lines with a single blank line
        text = Regex.Replace(text, pattern, "\n\n");

        // Convert the modified text back to a FormattedString
        formattedText = new FormattedString();
        formattedText.Spans.Add(new Span { Text = text });

        return formattedText;
    }
    private string GetProteinShakeHeader(string data)
    {
        //string searchTerm = "protein shake";
        //string[] parts = data.Split(':');

        //if (parts.Length > 1)
        //{
        //    string firstPart = parts[0] + ":";
        //    string secondPart = parts[1];

        //    // Find the index of the second occurrence of the search term in the second part
        //    int indexToRemove = secondPart.IndexOf(searchTerm, StringComparison.OrdinalIgnoreCase);
        //    if (indexToRemove != -1)
        //    {
        //        // Remove the second occurrence of the search term from the second part
        //        secondPart = secondPart.Remove(indexToRemove, searchTerm.Length);
        //    }

        //    // Concatenate the first part with the modified second part
        //    data = firstPart + secondPart;

        //}
        //return data;
        return "protein shake:";
    }
    private StringBuilder FormatLabelText(string text)
    {

        string[] lines = text.Split(new[] { Environment.NewLine }, StringSplitOptions.None);
        // Split the string into lines
        //string[] lines = text.Split('\n');

        // Create a formatted string
        //FormattedString formattedString = new FormattedString();
        StringBuilder formattedString = new StringBuilder();
        var lastLine = lines.LastOrDefault();
        if (text.ToLower().Contains("shopping"))
        {
            foreach (string line in lines)
            {
                if (!string.IsNullOrWhiteSpace(line) && !line.ToLower().Contains("ingredient"))
                {
                    string data = line;
                    data = data.Replace("[", "");
                    data = data.Replace("]", "");
                    data = data.Replace("*", "");
                    data = data.Replace("Stop here", "");
                    data = data.Replace("stop here", "");
                    // Check if the line contains a header (you can customize this condition)
                    if (data.ToLower().Contains("shopping"))
                    {
                        LblHeader.IsVisible = true;
                        LblHeader.Text = data;
                    }
                    else
                    {
                        if (!string.IsNullOrEmpty(data))
                            formattedString.AppendLine(data);
                    }
                }
            }
        }
        else
        {


            foreach (string line in lines)
            {
                if (!string.IsNullOrWhiteSpace(line) && !line.ToLower().Contains("ingredient"))
                {
                    string data = line;
                    data = data.Replace("[", "");
                    data = data.Replace("]", "");
                    data = data.Replace("Stop here", "");
                    data = data.Replace("stop here", "");
                    // Check if the line contains a header (you can customize this condition)
                    if (data.Contains(":") && (data.ToLower().Contains("breakfast") || data.ToLower().Contains("lunch") || data.ToLower().Contains("dinner") || data.ToLower().Contains("snack") || data.ToLower().Contains("protein shake") || data.ToLower().Contains("shopping") || data.ToLower().Contains("calories") || data.ToLower().Contains("meal plan:")))
                    {
                        if (data.ToLower().Contains("calories:"))
                            data = data.Replace("calories:", "calories");
                        if (data.ToLower().Contains("meal plan:"))
                            data = data.Replace("meal plan:", "meal plan");
                        LblHeader.IsVisible = true;
                        LblHeader.Text = data;
                        //Span headerSpan = new Span
                        //{
                        //    Text = data + "\n",
                        //    FontAttributes = FontAttributes.Bold,
                        //    FontSize = 19
                        //};

                        //// Add the span to the formatted string
                        //formattedString.Spans.Add(headerSpan);
                    }
                    else
                    {

                        // Create a new span for other lines
                        //Span regularSpan = new Span
                        //{
                        //    Text = (lastLine.ToLower().ToString() == line.ToLower().ToString()) ? data : data + "\n",
                        //    FontSize = 18
                        //};

                        // Add the span to the formatted string
                        if (!string.IsNullOrEmpty(data))
                            formattedString.AppendLine(data);
                    }
                }
            }
        }
        LblAnswer.Text = formattedString.ToString();
        // Set the formatted string to the label
        return formattedString;
    }

    private async void ChangeBubble(object sender, EventArgs e)
    {
        if (editBubbleImg.IsVisible == false)
            return;
        string text = "";
        var formattedText = LblAnswer.FormattedText;
        text = LblHeader.Text;
        text += "\r\n" + formattedText;
        //var text = LblAnswer.FormattedText;
        if (formattedText != null)
        {
            if (!string.IsNullOrEmpty(text.ToString()))
            {
                MessagingCenter.Send<string>(text.ToString(), "ChangeEachBubblePlan");
            }
        }
        else
        {
            string text1 = "";
            text1 = LblHeader.Text;
            text1 += "\r\n" + LblAnswer.Text;
            if (!string.IsNullOrEmpty(text1))
                MessagingCenter.Send<string>(text1, "ChangeEachBubblePlanFor3");
        }



    }

    private async void ChangeRecipe(object sender, EventArgs e)
    {
        try
        {
            if (editBubbleImg.IsVisible == false)
                return;
            string text = "";
            var formattedText = LblAnswer.FormattedText;
            text = LblHeader.Text;
            text += "\r\n" + formattedText;
            //var text = LblAnswer.FormattedText;
            if (formattedText != null)
            {
                if (!string.IsNullOrEmpty(text.ToString()))
                {
                    MessagingCenter.Send<string>(text.ToString(), "changeRecipeBubble");
                }
            }
        }
        catch (Exception ex)
        {

        }
    }

    private async void RegenerateMeal(object sender, EventArgs e)
    {
        if (editBubbleImg.IsVisible == false)
            return;
        string text = "";
        var formattedText = LblAnswer.FormattedText;
        text = LblHeader.Text;
        text += "\r\n" + formattedText;
        //var text = LblAnswer.FormattedText;
        if (formattedText != null)
        {
            if (!string.IsNullOrEmpty(text.ToString()))
            {
                MessagingCenter.Send<string>(text.ToString(), "regenerateBubblePlan");
            }
        }
    }

    private void Adjusting(object sender, EventArgs e)
    {
        //if (isAnimating)
        //{
        //    // Stop the animation
        //    AdjustingImage.IsAnimationPlaying = false;
        //    isAnimating = false;
        //}
        //else
        //{
        //    // Start the animation
        //    AdjustingImage.IsAnimationPlaying = true;
        //    isAnimating = true;
        //}
    }

    private void TipsEvent(object sender, EventArgs e)
    {
        var totalMacros = LocalDBManager.Instance.GetDBSetting("totalMacros")?.Value;
        if (totalMacros == null)
            totalMacros = "122,122,77";
        var modalPage = new Views.GeneralPopup("lamp.png", "", "", "");
        modalPage = new Views.GeneralPopup("medal.png", $"", $"", "Review tips", null, false, false, "false", "false", "false", "false", "true", "false", "false", false, true, totalMacros);
        Config.ShowTipsNumber += 1;
        PopupNavigation.Instance.PushAsync(modalPage);
    }

    public bool isMenuOpened { get; set; } = false;
    private async void menu_tapped(object sender, TappedEventArgs e)
    {
        try
        {
            if (isMenuOpened)
                return;
            isMenuOpened = true;

            //MessagingCenter.Send<string>("", "HideMealMenu");

            if (App.IsMealPlanMenuOpened)
                return;

            if (editBubbleImg.IsVisible == false)
                return;

            string headerText = "";
            headerText = LblHeader.Text;
            headerText = headerText.ToLower();
            if (headerText.StartsWith("shopping list") || headerText.StartsWith("final "))
                return;


            MenuButton.HeightRequest = 1;
            MenuButton.WidthRequest = 1;
            StackLayout s = ((StackLayout)sender);
            var editMenu = new MenuItem
            {
                Text = "Edit",
                Command = new Command(() => ChangeBubble(sender, e))
            };

            var regenerateMeal = new MenuItem
            {
                Text = "Regenerate",
                Command = new Command(() => RegenerateMeal(sender, e))
            };

            var recipeMenu = new MenuItem
            {
                Text = "Recipe",
                Command = new Command(() => ChangeRecipe(sender, e))
            };

            ((ContextMenuButton)s.Children[0]).ItemsContainerHeight = 150;
            ((ContextMenuButton)s.Children[0]).Items = new[] { editMenu, recipeMenu, regenerateMeal };

            ((ContextMenuButton)s.Children[0]).ContextMenuButton_Clicked(sender, e);
            //return;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error in menu_tapped: {ex.Message}");
        }
        finally
        {
            await Task.Delay(300);
            isMenuOpened = false;
        }
    }

    private async void menu_tapped1(object sender, TappedEventArgs e)
    {
        try
        {
            if (isMenuOpened)
                return;

            isMenuOpened = true;

            if (App.IsMealPlanMenuOpened)
                return;

            if (editBubbleImg.IsVisible == false)
                return;

            MenuButton.HeightRequest = 1;
            MenuButton.WidthRequest = 1;
            StackLayout s = (((((((Grid)((Frame)sender).Parent).Parent).Parent).Parent).Parent).Parent).Parent as StackLayout;
            var editMenu = new MenuItem
            {
                Text = "Edit",
                Command = new Command(() => ChangeBubble(sender, e))
            };

            var regenerateMeal = new MenuItem
            {
                Text = "Regenerate",
                Command = new Command(() => RegenerateMeal(sender, e))
            };

            var recipeMenu = new MenuItem
            {
                Text = "Recipe",
                Command = new Command(() => ChangeRecipe(sender, e))
            };

            ((ContextMenuButton)s.Children[0]).ItemsContainerHeight = 150;
            ((ContextMenuButton)s.Children[0]).Items = new[] { editMenu, recipeMenu, regenerateMeal };

            ((ContextMenuButton)s.Children[0]).ContextMenuButton_Clicked(sender, e);
            //return;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error in menu_tapped: {ex.Message}");
        }
        finally
        {
            await Task.Delay(300);
            isMenuOpened = false;
        }
    }
}
