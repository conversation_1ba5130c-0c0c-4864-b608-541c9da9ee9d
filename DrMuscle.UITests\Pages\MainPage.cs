using OpenQA.Selenium;
using OpenQA.Selenium.Appium;
using OpenQA.Selenium.Support.UI;
using System;

namespace DrMuscle.UITests.Pages
{
    /// <summary>
    /// Page object for the main tabbed interface after login
    /// </summary>
    public class MainPage
    {
        private readonly AppiumDriver? _driver;
        private readonly WebDriverWait _wait;
        
        public MainPage(AppiumDriver driver)
        {
            _driver = driver ?? throw new ArgumentNullException(nameof(driver));
            _wait = new WebDriverWait(_driver, TimeSpan.FromSeconds(10));
        }
        
        // Tab elements
        private AppiumElement? HomeTab => FindElement(MobileBy.AccessibilityId("HomeTab"));
        private AppiumElement? HistoryTab => FindElement(MobileBy.AccessibilityId("HistoryTab"));
        private AppiumElement? ChatTab => FindElement(MobileBy.AccessibilityId("ChatTab"));
        private AppiumElement? SettingsTab => FindElement(MobileBy.AccessibilityId("SettingsTab"));
        
        // Actions
        public void NavigateToHome()
        {
            HomeTab?.Click();
        }
        
        public void NavigateToHistory()
        {
            HistoryTab?.Click();
        }
        
        public void NavigateToChat()
        {
            ChatTab?.Click();
        }
        
        public void NavigateToSettings()
        {
            SettingsTab?.Click();
        }
        
        // Verifications
        public bool IsDisplayed()
        {
            return HomeTab != null || HistoryTab != null || ChatTab != null || SettingsTab != null;
        }
        
        public bool IsHomeTabActive()
        {
            return IsTabActive(HomeTab);
        }
        
        public bool IsHistoryTabActive()
        {
            return IsTabActive(HistoryTab);
        }
        
        public bool IsChatTabActive()
        {
            return IsTabActive(ChatTab);
        }
        
        public bool IsSettingsTabActive()
        {
            return IsTabActive(SettingsTab);
        }
        
        public void WaitForPageToLoad()
        {
            _wait.Until(d => IsDisplayed());
        }
        
        // Helper methods
        private AppiumElement? FindElement(By by)
        {
            try
            {
                return _driver?.FindElement(by) as AppiumElement;
            }
            catch (NoSuchElementException)
            {
                return null;
            }
        }
        
        #pragma warning disable CA1822 // Mark members as static
        private bool IsTabActive(AppiumElement? tab)
        {
            if (tab == null) return false;
            
            // Check if tab is selected/active
            // This might vary based on platform
            try
            {
                var isSelected = tab.GetAttribute("selected");
                if (!string.IsNullOrEmpty(isSelected))
                {
                    return bool.Parse(isSelected);
                }
                
                // Alternative check for iOS
                var value = tab.GetAttribute("value");
                if (!string.IsNullOrEmpty(value) && value.Contains('1'))
                {
                    return true;
                }
                
                // Check by visual properties or other attributes
                return false;
            }
            catch
            {
                return false;
            }
        }
        #pragma warning restore CA1822 // Mark members as static
    }
}