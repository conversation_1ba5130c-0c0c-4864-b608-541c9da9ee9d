using OpenQA.Selenium;
using OpenQA.Selenium.Appium;
using OpenQA.Selenium.Support.UI;
using System;
using System.Collections.Generic;
using System.Linq;

namespace DrMuscle.UITests.Pages
{
    /// <summary>
    /// Page object for RIR (Reps in Reserve) functionality
    /// </summary>
    public class RIRPage
    {
        private readonly AppiumDriver? _driver;
        private readonly WebDriverWait _wait;
        
        public RIRPage(AppiumDriver driver)
        {
            _driver = driver ?? throw new ArgumentNullException(nameof(driver));
            _wait = new WebDriverWait(_driver, TimeSpan.FromSeconds(10));
        }
        
        // RIR prompt elements
        private AppiumElement? RIRPromptModal => FindElement(MobileBy.AccessibilityId("RIRPromptModal"));
        private AppiumElement? RIRTitle => FindElement(MobileBy.AccessibilityId("RIRPromptTitle"));
        private AppiumElement? RIRGuidanceLabel => FindElement(MobileBy.AccessibilityId("RIRGuidanceText"));
        private AppiumElement? RIRScale => FindElement(MobileBy.AccessibilityId("RIRScale"));
        private AppiumElement? RIRConfirmationMessage => FindElement(MobileBy.AccessibilityId("RIRConfirmation"));
        
        // RIR option buttons
        private AppiumElement? RIR0Button => FindElement(MobileBy.AccessibilityId("RIR0"));
        private AppiumElement? RIR1Button => FindElement(MobileBy.AccessibilityId("RIR1"));
        private AppiumElement? RIR2Button => FindElement(MobileBy.AccessibilityId("RIR2"));
        private AppiumElement? RIR3Button => FindElement(MobileBy.AccessibilityId("RIR3"));
        private AppiumElement? RIR4Button => FindElement(MobileBy.AccessibilityId("RIR4"));
        private AppiumElement? RIR5PlusButton => FindElement(MobileBy.AccessibilityId("RIR5Plus"));
        
        // RIR descriptions
        private AppiumElement? RIR0Description => FindElement(MobileBy.AccessibilityId("RIR0Desc"));
        private AppiumElement? RIR1Description => FindElement(MobileBy.AccessibilityId("RIR1Desc"));
        private AppiumElement? RIR2Description => FindElement(MobileBy.AccessibilityId("RIR2Desc"));
        private AppiumElement? RIR3Description => FindElement(MobileBy.AccessibilityId("RIR3Desc"));
        private AppiumElement? RIR4Description => FindElement(MobileBy.AccessibilityId("RIR4Desc"));
        private AppiumElement? RIR5PlusDescription => FindElement(MobileBy.AccessibilityId("RIR5PlusDesc"));
        
        // Warning elements
        private AppiumElement? FailureWarningLabel => FindElement(MobileBy.AccessibilityId("FailureWarning"));
        
        // Actions
        public void SelectRIR(string rirValue)
        {
            AppiumElement? rirButton = rirValue switch
            {
                "0" => RIR0Button,
                "1" => RIR1Button,
                "2" => RIR2Button,
                "3" => RIR3Button,
                "4" => RIR4Button,
                "5+" or "5" => RIR5PlusButton,
                _ => null
            };
            
            if (rirButton != null)
            {
                rirButton.Click();
                Thread.Sleep(500); // Allow for animation
            }
            else
            {
                // Try finding by text if accessibility ID doesn't work
                var rirOption = FindElement(MobileBy.XPath($"//XCUIElementTypeButton[contains(@name, 'RIR {rirValue}')]"));
                rirOption?.Click();
            }
        }
        
        public void HighlightRIR(string rirValue)
        {
            // Simulate hovering or focusing on RIR option to show details
            AppiumElement? rirButton = rirValue switch
            {
                "0" => RIR0Button,
                "1" => RIR1Button,
                "2" => RIR2Button,
                "3" => RIR3Button,
                "4" => RIR4Button,
                "5+" or "5" => RIR5PlusButton,
                _ => null
            };
            
            // On mobile, we might need to long press or tap to show details
            if (rirButton != null)
            {
                // Perform a tap to highlight/focus
                rirButton.Click();
                Thread.Sleep(200);
            }
        }
        
        // Verifications
        public bool IsRIRPromptVisible()
        {
            return RIRPromptModal != null && RIRPromptModal.Displayed;
        }
        
        public bool WaitForRIRPrompt(int timeoutSeconds = 5)
        {
            try
            {
                var shortWait = new WebDriverWait(_driver!, TimeSpan.FromSeconds(timeoutSeconds));
                shortWait.Until(d => IsRIRPromptVisible());
                return true;
            }
            catch (WebDriverTimeoutException)
            {
                return false;
            }
        }
        
        // Data retrieval
        public List<string> GetRIROptions()
        {
            var options = new List<string>();
            
            if (RIR0Button != null) options.Add("0");
            if (RIR1Button != null) options.Add("1");
            if (RIR2Button != null) options.Add("2");
            if (RIR3Button != null) options.Add("3");
            if (RIR4Button != null) options.Add("4");
            if (RIR5PlusButton != null) options.Add("5+");
            
            // If buttons aren't found by accessibility ID, try finding by class
            if (options.Count == 0 && RIRScale != null)
            {
                var rirButtons = RIRScale.FindElements(MobileBy.ClassName("XCUIElementTypeButton"));
                options = rirButtons.Select(b => ExtractRIRValue(b.Text)).Where(v => !string.IsNullOrEmpty(v)).ToList();
            }
            
            return options;
        }
        
        public Dictionary<string, string> GetRIRDescriptions()
        {
            var descriptions = new Dictionary<string, string>();
            
            if (RIR0Description != null) 
                descriptions["0"] = RIR0Description.Text;
            if (RIR1Description != null) 
                descriptions["1"] = RIR1Description.Text;
            if (RIR2Description != null) 
                descriptions["2"] = RIR2Description.Text;
            if (RIR3Description != null) 
                descriptions["3"] = RIR3Description.Text;
            if (RIR4Description != null) 
                descriptions["4"] = RIR4Description.Text;
            if (RIR5PlusDescription != null) 
                descriptions["5+"] = RIR5PlusDescription.Text;
            
            // Fallback: look for description elements by pattern
            if (descriptions.Count == 0)
            {
                var descElements = _driver?.FindElements(MobileBy.XPath("//XCUIElementTypeStaticText[contains(@name, 'RIR')]"));
                if (descElements != null)
                {
                    foreach (var elem in descElements)
                    {
                        var text = elem.Text;
                        var rir = ExtractRIRValue(text);
                        if (!string.IsNullOrEmpty(rir))
                        {
                            descriptions[rir] = text;
                        }
                    }
                }
            }
            
            return descriptions;
        }
        
        public string GetRIRGuidanceText()
        {
            return RIRGuidanceLabel?.Text ?? "";
        }
        
        public string GetFailureWarning()
        {
            return FailureWarningLabel?.Text ?? "";
        }
        
        public string GetConfirmationMessage()
        {
            return RIRConfirmationMessage?.Text ?? "";
        }
        
        public string GetRIRPromptTitle()
        {
            return RIRTitle?.Text ?? "";
        }
        
        // Helper methods
        private AppiumElement? FindElement(By by)
        {
            try
            {
                return _driver?.FindElement(by) as AppiumElement;
            }
            catch (NoSuchElementException)
            {
                return null;
            }
        }
        
        #pragma warning disable CA1822 // Mark members as static
        private string ExtractRIRValue(string text)
        #pragma warning restore CA1822 // Mark members as static
        {
            // Extract RIR value from text like "RIR 2" or "2 reps in reserve"
            if (text.Contains("RIR 0") || text.Contains("0 reps")) return "0";
            if (text.Contains("RIR 1") || text.Contains("1 rep")) return "1";
            if (text.Contains("RIR 2") || text.Contains("2 reps")) return "2";
            if (text.Contains("RIR 3") || text.Contains("3 reps")) return "3";
            if (text.Contains("RIR 4") || text.Contains("4 reps")) return "4";
            if (text.Contains("RIR 5") || text.Contains("5+ reps") || text.Contains("5 or more")) return "5+";
            
            return "";
        }
    }
}