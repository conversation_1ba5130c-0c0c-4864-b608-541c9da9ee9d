using OpenQA.Selenium;
using OpenQA.Selenium.Appium;
using OpenQA.Selenium.Support.UI;
using System;
using System.Collections.Generic;
using System.Linq;

namespace DrMuscle.UITests.Pages
{
    /// <summary>
    /// Page object for the workout screens
    /// </summary>
    public class WorkoutPage
    {
        private readonly AppiumDriver? _driver;
        private readonly WebDriverWait _wait;
        
        public WorkoutPage(AppiumDriver driver)
        {
            _driver = driver ?? throw new ArgumentNullException(nameof(driver));
            _wait = new WebDriverWait(_driver, TimeSpan.FromSeconds(10));
        }
        
        // Home page elements
        private AppiumElement? StartWorkoutButton => FindElement(MobileBy.AccessibilityId("StartWorkoutButton"));
        private AppiumElement? ContinueWorkoutButton => FindElement(MobileBy.AccessibilityId("ContinueWorkoutButton"));
        
        // Exercise list elements
        private AppiumElement? ExerciseList => FindElement(MobileBy.AccessibilityId("ExerciseList"));
        private AppiumElement? FirstExercise => FindElement(MobileBy.AccessibilityId("ExerciseItem0"));
        
        // Exercise detail elements
        private AppiumElement? RepsInput => FindElement(MobileBy.AccessibilityId("RepsInput"));
        private AppiumElement? WeightInput => FindElement(MobileBy.AccessibilityId("WeightInput"));
        private AppiumElement? SaveSetButton => FindElement(MobileBy.AccessibilityId("SaveSetButton"));
        private AppiumElement? FinishExerciseButton => FindElement(MobileBy.AccessibilityId("FinishExerciseButton"));
        
        // Workout summary elements
        private AppiumElement? FinishWorkoutButton => FindElement(MobileBy.AccessibilityId("FinishWorkoutButton"));
        private AppiumElement? WorkoutSummary => FindElement(MobileBy.AccessibilityId("WorkoutSummary"));
        // Note: Total sets and weight labels might be dynamically generated
        // We'll need to use XPath or other selectors for these
        private AppiumElement? TotalSetsLabel => FindElement(MobileBy.XPath("//XCUIElementTypeStaticText[contains(@name, 'sets')]"));
        private AppiumElement? TotalWeightLabel => FindElement(MobileBy.XPath("//XCUIElementTypeStaticText[contains(@name, 'lbs') or contains(@name, 'kg')]"));
        
        // Actions
        public void StartWorkout()
        {
            StartWorkoutButton?.Click();
        }
        
        public void ContinueWorkout()
        {
            ContinueWorkoutButton?.Click();
        }
        
        public void SelectFirstExercise()
        {
            FirstExercise?.Click();
        }
        
        public void EnterSet(string reps, string weight)
        {
            RepsInput?.Clear();
            RepsInput?.SendKeys(reps);
            
            WeightInput?.Clear();
            WeightInput?.SendKeys(weight);
        }
        
        public void SaveSet()
        {
            SaveSetButton?.Click();
        }
        
        public void FinishExercise()
        {
            FinishExerciseButton?.Click();
        }
        
        public void FinishWorkout()
        {
            FinishWorkoutButton?.Click();
        }
        
        // Verifications
        public bool IsStartWorkoutVisible()
        {
            return StartWorkoutButton != null && StartWorkoutButton.Displayed;
        }
        
        public bool IsExerciseListVisible()
        {
            return ExerciseList != null && ExerciseList.Displayed;
        }
        
        public bool IsWorkoutSummaryVisible()
        {
            return WorkoutSummary != null && WorkoutSummary.Displayed;
        }
        
        public string GetTotalSets()
        {
            return TotalSetsLabel?.Text ?? "";
        }
        
        public string GetTotalWeight()
        {
            return TotalWeightLabel?.Text ?? "";
        }
        
        public void WaitForStartWorkout()
        {
            _wait.Until(d => IsStartWorkoutVisible());
        }
        
        public void WaitForExerciseList()
        {
            _wait.Until(d => IsExerciseListVisible());
        }
        
        public void WaitForWorkoutSummary()
        {
            _wait.Until(d => IsWorkoutSummaryVisible());
        }
        
        // Additional elements for comprehensive tests
        private AppiumElement? ExerciseNameLabel => FindElement(MobileBy.AccessibilityId("CurrentExerciseName"));
        private AppiumElement? RecommendedWeightLabel => FindElement(MobileBy.AccessibilityId("RecommendedWeight"));
        private AppiumElement? RIRTargetLabel => FindElement(MobileBy.AccessibilityId("RIRTargetRange"));
        private AppiumElement? DeloadSuggestionLabel => FindElement(MobileBy.AccessibilityId("DeloadSuggestion"));
        private AppiumElement? PlateCalculatorIcon => FindElement(MobileBy.AccessibilityId("PlateCalculatorIcon"));
        private AppiumElement? SettingsButton => FindElement(MobileBy.AccessibilityId("SettingsButton"));
        
        // Additional actions
        public void SelectExerciseByName(string exerciseName)
        {
            var exerciseCell = FindElement(MobileBy.XPath($"//XCUIElementTypeCell[contains(@name, '{exerciseName}')]")) ??
                              FindElement(MobileBy.AccessibilityId(exerciseName));
            exerciseCell?.Click();
        }
        
        public void EnterWeight(string weight, string unit = "lbs")
        {
            WeightInput?.Clear();
            WeightInput?.SendKeys(weight);
        }
        
        public bool CanEnterNextSet()
        {
            return RepsInput != null && RepsInput.Enabled && 
                   WeightInput != null && WeightInput.Enabled;
        }
        
        public void NavigateToSettings()
        {
            SettingsButton?.Click();
        }
        
        public bool HasPlateCalculator()
        {
            return PlateCalculatorIcon != null && PlateCalculatorIcon.Displayed;
        }
        
        // Additional data retrieval methods
        public string GetCurrentExerciseName()
        {
            return ExerciseNameLabel?.Text ?? "";
        }
        
        public string GetRecommendedWeight()
        {
            return RecommendedWeightLabel?.Text ?? WeightInput?.Text ?? "";
        }
        
        public string GetRIRTargetRange()
        {
            return RIRTargetLabel?.Text ?? "";
        }
        
        public string GetDeloadSuggestion()
        {
            return DeloadSuggestionLabel?.Text ?? "";
        }
        
        public string GetTargetReps()
        {
            return RepsInput?.GetAttribute("placeholder") ?? RepsInput?.Text ?? "";
        }
        
        public void EnterReps(string reps)
        {
            RepsInput?.Clear();
            RepsInput?.SendKeys(reps);
        }
        
        public string GetLastSetDetails()
        {
            var lastSetElement = FindElement(MobileBy.AccessibilityId("LastSetDetails"));
            return lastSetElement?.Text ?? "";
        }
        
        public string GetCompletedSetCount()
        {
            var setCountElement = FindElement(MobileBy.AccessibilityId("CompletedSetCount"));
            return setCountElement?.Text ?? "";
        }
        
        public void StartNextWorkout()
        {
            // Simulate starting next workout session
            var nextWorkoutButton = FindElement(MobileBy.AccessibilityId("StartNextWorkout"));
            nextWorkoutButton?.Click();
        }
        
        public List<string> GetExerciseList()
        {
            var exercises = new List<string>();
            
            if (ExerciseList != null)
            {
                var cells = ExerciseList.FindElements(MobileBy.ClassName("XCUIElementTypeCell"));
                exercises = cells.Select(c => c.Text).ToList();
            }
            
            return exercises;
        }
        
        public void NavigateToHome()
        {
            var homeButton = FindElement(MobileBy.AccessibilityId("HomeButton")) ??
                            FindElement(MobileBy.AccessibilityId("HomeTab"));
            homeButton?.Click();
        }
        
        // Helper methods
        private AppiumElement? FindElement(By by)
        {
            try
            {
                return _driver?.FindElement(by) as AppiumElement;
            }
            catch (NoSuchElementException)
            {
                return null;
            }
        }
    }
}