import XCTest
@testable import DrMuscleWatchApp

class DrMuscleAPIClientTests: XCTestCase {
    var sut: DrMuscleAPIClient!
    
    override func setUp() {
        super.setUp()
        sut = DrMuscleAPIClient.shared
        // Clear any stored auth token
        sut.clearAuthToken()
    }
    
    override func tearDown() {
        sut.clearAuthToken()
        sut = nil
        super.tearDown()
    }
    
    // MARK: - Sign in with Apple Tests
    
    func testSignInWithAppleEndpointConfiguration() async throws {
        // Given the API client
        // When checking the sign in with Apple method
        // Then it should:
        // - Use POST method
        // - Target endpoint: "api/Account/RegisterWithApple"
        // - Send token in request body
        // - Return UserInfosModel
        
        // This test verifies the method exists and has the correct signature
        let testToken = "test-apple-id-token"
        
        // We can't test the actual network call without mocking
        // but we can verify the method exists with the correct parameters
        XCTAssertNotNil(sut)
    }
    
    func testSignInWithAppleRequestBody() {
        // Given a token for Sign in with Apple
        let testToken = "test-apple-id-token"
        
        // When the request is prepared
        // Then the body should contain:
        // {
        //   "token": "test-apple-id-token"
        // }
        
        // This documents the expected request format
        let expectedBody = ["token": testToken]
        XCTAssertEqual(expectedBody["token"], testToken)
    }
    
    func testSignInWithAppleResponseParsing() throws {
        // Given a successful response from the backend
        let mockResponseData = """
        {
            "id": "user123",
            "email": "<EMAIL>",
            "token": "auth-token-from-backend",
            "firstName": "Test",
            "lastName": "User"
        }
        """.data(using: .utf8)!
        
        // When parsing the response
        let decoder = JSONDecoder()
        let userInfo = try decoder.decode(UserInfosModel.self, from: mockResponseData)
        
        // Then it should correctly parse all fields
        XCTAssertEqual(userInfo.id, "user123")
        XCTAssertEqual(userInfo.email, "<EMAIL>")
        XCTAssertEqual(userInfo.token, "auth-token-from-backend")
        XCTAssertEqual(userInfo.firstName, "Test")
        XCTAssertEqual(userInfo.lastName, "User")
    }
    
    // MARK: - Token Management Tests
    
    func testSetAuthToken() {
        // Given an auth token
        let token = "test-auth-token"
        
        // When setting the token
        sut.setAuthToken(token)
        
        // Then it should be stored (we can't directly verify without exposing internals)
        // but we can verify the method exists
        XCTAssertNotNil(sut)
    }
    
    func testClearAuthToken() {
        // Given a stored auth token
        sut.setAuthToken("test-token")
        
        // When clearing the token
        sut.clearAuthToken()
        
        // Then the token should be cleared
        // (we can't directly verify without exposing internals)
        XCTAssertNotNil(sut)
    }
    
    // MARK: - Error Handling Tests
    
    func testSignInWithAppleNetworkError() async {
        // Given the API client
        // When a network error occurs during sign in
        // Then it should throw an appropriate error
        
        // Note: Without dependency injection or mocking, we can't test actual network errors
        // This test documents expected error handling behavior
        
        // Expected errors:
        // - Network unavailable
        // - Invalid response
        // - Server error (5xx)
        // - Authentication error (401)
        XCTAssertNotNil(sut)
    }
    
    func testSignInWithAppleInvalidTokenError() async {
        // Given an invalid Apple ID token
        let invalidToken = ""
        
        // When attempting to sign in
        // Then it should handle the error appropriately
        
        // Expected backend response for invalid token:
        // - 400 Bad Request
        // - Error message in response body
        XCTAssertNotNil(sut)
    }
}

// MARK: - Integration Test Documentation

extension DrMuscleAPIClientTests {
    
    func testSignInWithAppleFullFlow() {
        // This documents the full Sign in with Apple flow:
        
        // 1. User taps Sign in with Apple button
        // 2. LoginViewModel.startSignInWithApple() is called
        // 3. ASAuthorizationController presents Apple's auth UI
        // 4. User completes authentication
        // 5. AuthenticationManager receives the Apple ID credential
        // 6. AuthenticationManager extracts the identity token
        // 7. DrMuscleAPIClient.signInWithApple(idToken:) is called
        // 8. Backend validates the token with Apple
        // 9. Backend returns UserInfosModel with Dr. Muscle auth token
        // 10. AuthenticationManager stores the user info and token
        // 11. App navigates to authenticated state
        
        XCTAssertTrue(true, "Flow documentation")
    }
    
    func testExpectedAPIEndpoints() {
        // Document all authentication-related endpoints:
        
        // POST /api/Account/RegisterWithApple
        // - Body: { "token": "apple-id-token" }
        // - Response: UserInfosModel
        // - Used for: Initial sign in and subsequent sign ins
        
        // The same endpoint handles both:
        // - New user registration (creates Dr. Muscle account)
        // - Existing user sign in (returns existing account)
        
        XCTAssertTrue(true, "Endpoint documentation")
    }
} 