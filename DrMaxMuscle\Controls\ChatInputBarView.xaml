<?xml version="1.0" encoding="utf-8" ?>
<ContentView xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:controls="clr-namespace:DrMaxMuscle.Controls"
             x:Class="DrMaxMuscle.Controls.ChatInputBarView">
    <Grid RowSpacing="0" ColumnSpacing="8" x:Name="frameGrid" Margin="{OnPlatform Android='2,0' , iOS= '8,0'}">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
        </Grid.RowDefinitions>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="*" />
            <ColumnDefinition Width="55" />
        </Grid.ColumnDefinitions>

          <Border
                Margin="0"
                Stroke="Transparent" StrokeShape="RoundRectangle 10,10,10,10" Padding="5,7,5,5" 
                BackgroundColor="{OnPlatform Android='#E2E2E2',iOS='#E2E2E2'}">
            <controls:ExtendedEditorControl 
            x:Name="chatTextInput"
            IsTextPredictionEnabled="True"
            BackgroundColor="#E2E2E2"
            IsSpellCheckEnabled="True" 
            Focused="chatTextInput_Focused"
            Unfocused="chatTextInput_Unfocused"
            Text="{Binding MessageText,Mode=TwoWay}"
            Margin="{OnPlatform Android='5,1',iOS='5,5,5,1'}"
            TextColor="Black" 
            IsExpandable="true"
            HorizontalOptions="FillAndExpand"
            PlaceholderColor="#616161"
            Placeholder="Message" 
            Grid.Row="0" 
            Grid.Column="0"
            TextChanged="chatTextInput_TextChanged" />
        </Border>
        <Frame
        HasShadow="False"
        Padding="8,8,10,8"
        Margin="0"
        HeightRequest="55"
        VerticalOptions="End"
        x:Name="frmSendMessage"
        CornerRadius="10"
        BackgroundColor="#224968"
        Grid.Row="0" 
        Grid.Column="1" >
            <Image Source="sendmesageicon" HeightRequest="30" WidthRequest="30"  Aspect="AspectFit"/>
            <Frame.GestureRecognizers>
                <TapGestureRecognizer Tapped="Handle_Completed" />
            </Frame.GestureRecognizers>
        </Frame>
        <!--<Label Text="Send" FontAttributes="Bold" FontSize="15" HorizontalOptions="Center" Grid.Row="0" Grid.Column="1" TextColor="{x:Static app:AppThemeConstants.BlueColor}" x:Name="BtnSend" VerticalOptions="Center" VerticalTextAlignment="Center">
        <Label.GestureRecognizers>
            <TapGestureRecognizer Tapped="Handle_Completed" />
        </Label.GestureRecognizers>
    </Label>-->
    </Grid>
</ContentView>
