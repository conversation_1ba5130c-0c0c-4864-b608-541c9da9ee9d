using System;
using System.IO;
using System.Diagnostics;
using OpenQA.Selenium.Appium.Service;
using OpenQA.Selenium.Support.UI;

namespace DrMuscle.UITests
{
    public class AppiumSetup
    {
        private static AppiumLocalService? _appiumLocalService;
        protected static IOSDriver? Driver { get; private set; }
        
        [OneTimeSetUp]
        public void OneTimeSetup()
        {
            try
            {
                // Start Appium server if running locally (not in CI)
                if (Environment.GetEnvironmentVariable("CI") != "true")
                {
                    var appiumServiceBuilder = new AppiumServiceBuilder()
                        .UsingAnyFreePort()
                        .WithLogFile(new FileInfo(Path.Combine(TestContext.CurrentContext.WorkDirectory, "appium.log")));
                    
                    _appiumLocalService = appiumServiceBuilder.Build();
                    _appiumLocalService.Start();
                }
                
                // Configure iOS driver options
                var options = new AppiumOptions();
                
                // Use the W3C WebDriver protocol compliant options
                // For Appium.WebDriver 5.0.0, standard W3C capabilities are set directly
                options.PlatformName = "iOS";
                options.AutomationName = "XCUITest";
                
                // Get platform version from environment or use default
                var platformVersion = Environment.GetEnvironmentVariable("IOS_PLATFORM_VERSION") ?? "18.4";
                options.PlatformVersion = platformVersion;
                
                // Device name can also be configurable
                var deviceName = Environment.GetEnvironmentVariable("IOS_DEVICE_NAME") ?? "iPhone 15 Pro";
                options.DeviceName = deviceName;
                
                // Use the already booted device if in CI
                if (Environment.GetEnvironmentVariable("CI") == "true")
                {
                    try
                    {
                        // Get the booted device ID like SimCtlTestRunner does
                        var bootedDeviceId = GetBootedDeviceId();
                        if (!string.IsNullOrEmpty(bootedDeviceId))
                        {
                            Console.WriteLine($"Using already booted device: {bootedDeviceId}");
                            options.AddAdditionalAppiumOption("appium:udid", bootedDeviceId);
                        }
                        else
                        {
                            options.AddAdditionalAppiumOption("appium:udid", "auto");
                        }
                    }
                    catch
                    {
                        // Fallback to auto if we can't find booted device
                        options.AddAdditionalAppiumOption("appium:udid", "auto");
                    }
                }
                else
                {
                    options.AddAdditionalAppiumOption("appium:udid", "auto");
                }
                
                Console.WriteLine("Appium options configured:");
                Console.WriteLine("  Platform: iOS");
                Console.WriteLine("  Automation: XCUITest");
                Console.WriteLine($"  Device: {deviceName}");
                Console.WriteLine($"  Platform Version: {platformVersion}");
                
                // Get app bundle path from environment or search for it
                var appPath = Environment.GetEnvironmentVariable("IOS_APP_BUNDLE");
                Console.WriteLine($"IOS_APP_BUNDLE from environment: {appPath ?? "not set"}");
                
                if (string.IsNullOrEmpty(appPath))
                {
                    Console.WriteLine("Searching for app bundle...");
                    appPath = FindAppBundle();
                }
                
                if (!string.IsNullOrEmpty(appPath))
                {
                    Console.WriteLine($"Using app path: {appPath}");
                    options.App = appPath;
                }
                else
                {
                    Console.WriteLine("No app path found, using bundle ID: com.drmaxmuscle.max");
                    // Use bundle ID if app is already installed
                    options.AddAdditionalAppiumOption("appium:bundleId", "com.drmaxmuscle.max");
                }
                
                // Additional iOS-specific options
                options.AddAdditionalAppiumOption("appium:noReset", true);
                options.AddAdditionalAppiumOption("appium:newCommandTimeout", 300);
                options.AddAdditionalAppiumOption("appium:showXcodeLog", Environment.GetEnvironmentVariable("CI") != "true");
                options.AddAdditionalAppiumOption("appium:wdaLaunchTimeout", 120000);
                
                // CI-specific options
                if (Environment.GetEnvironmentVariable("CI") == "true")
                {
                    Console.WriteLine("Running in CI mode - using already booted simulator");
                    options.AddAdditionalAppiumOption("appium:simulatorStartupTimeout", 360000);
                    
                    // Prevent Appium from trying to open Simulator UI in headless CI
                    options.AddAdditionalAppiumOption("appium:isHeadless", true);
                    options.AddAdditionalAppiumOption("appium:showIOSLog", false);
                    options.AddAdditionalAppiumOption("appium:simulatorWindowCenter", false);
                    options.AddAdditionalAppiumOption("appium:simulatorWindowBringToFront", false);
                }
                
                // Create the driver
                var serverUrl = Environment.GetEnvironmentVariable("APPIUM_SERVER_URL") ?? "http://127.0.0.1:4723/";
                var serverUri = _appiumLocalService?.ServiceUrl ?? new Uri(serverUrl);
                Console.WriteLine($"Connecting to Appium server at: {serverUri}");
                Driver = new IOSDriver(serverUri, options, TimeSpan.FromSeconds(120));
                
                // Set implicit wait
                Driver.Manage().Timeouts().ImplicitWait = TimeSpan.FromSeconds(10);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Failed to initialize Appium driver: {ex.Message}");
                Console.WriteLine($"Exception type: {ex.GetType().Name}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
                
                if (ex.InnerException != null)
                {
                    Console.WriteLine($"Inner exception: {ex.InnerException.Message}");
                }
                
                // In CI, provide more detailed error information
                if (Environment.GetEnvironmentVariable("CI") == "true")
                {
                    var appBundle = FindAppBundle();
                    if (string.IsNullOrEmpty(appBundle))
                    {
                        Assert.Ignore($"App bundle not found. Check IOS_APP_BUNDLE environment variable or build output. Error: {ex.Message}");
                    }
                    else
                    {
                        Assert.Ignore($"Failed to connect to Appium server. Ensure Appium is running on port 4723. Error: {ex.Message}");
                    }
                }
                else
                {
                    throw;
                }
            }
        }
        
        [OneTimeTearDown]
        public void OneTimeTearDown()
        {
            Driver?.Quit();
            Driver?.Dispose();
            Driver = null;
            
            _appiumLocalService?.Dispose();
            _appiumLocalService = null;
        }
        
        private static string? FindAppBundle()
        {
            // First check environment variable (used in CI)
            var envAppBundle = Environment.GetEnvironmentVariable("IOS_APP_BUNDLE");
            if (!string.IsNullOrEmpty(envAppBundle) && Directory.Exists(envAppBundle))
            {
                Console.WriteLine($"Using app bundle from environment variable: {envAppBundle}");
                return envAppBundle;
            }
            
            // Fall back to searching common paths
            var searchPaths = new[]
            {
                "../DrMaxMuscle/bin/Release/net8.0-ios/iossimulator-x64/DrMaxMuscle.app",
                "../DrMaxMuscle/bin/Debug/net8.0-ios/iossimulator-x64/DrMaxMuscle.app",
                "../DrMaxMuscle/bin/Release/net8.0-ios/DrMaxMuscle.app",
                "../DrMaxMuscle/bin/Debug/net8.0-ios/DrMaxMuscle.app"
            };
            
            foreach (var path in searchPaths)
            {
                var fullPath = Path.GetFullPath(path);
                if (Directory.Exists(fullPath))
                {
                    Console.WriteLine($"Found app bundle at: {fullPath}");
                    return fullPath;
                }
            }
            
            return null;
        }
        
        private static string? GetBootedDeviceId()
        {
            try
            {
                var startInfo = new ProcessStartInfo
                {
                    FileName = "xcrun",
                    Arguments = "simctl list devices booted",
                    RedirectStandardOutput = true,
                    UseShellExecute = false,
                    CreateNoWindow = true
                };
                
                using var process = Process.Start(startInfo);
                if (process == null) return null;
                
                var output = process.StandardOutput.ReadToEnd();
                process.WaitForExit();
                
                var lines = output.Split('\n', StringSplitOptions.RemoveEmptyEntries);
                foreach (var line in lines)
                {
                    if (line.Contains("(Booted)") && line.Contains("iPhone"))
                    {
                        // Extract device ID from line like: iPhone 14 (ABC123...) (Booted)
                        var start = line.IndexOf('(');
                        var end = line.IndexOf(')', start);
                        if (start > 0 && end > start)
                        {
                            return line.Substring(start + 1, end - start - 1);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error getting booted device ID: {ex.Message}");
            }
            
            return null;
        }
        
        protected static IWebElement FindUIElement(string automationId)
        {
            return Driver?.FindElement(MobileBy.AccessibilityId(automationId)) 
                ?? throw new InvalidOperationException("Driver not initialized");
        }
        
        protected static void WaitForElement(string automationId, int timeoutSeconds = 30)
        {
            var wait = new WebDriverWait(Driver, TimeSpan.FromSeconds(timeoutSeconds));
            wait.Until(d => d.FindElement(MobileBy.AccessibilityId(automationId)));
        }
    }
}