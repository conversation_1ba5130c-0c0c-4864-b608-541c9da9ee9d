import XCTest
import AuthenticationServices
@testable import DrMuscleWatchApp

class AuthenticationManagerTests: XCTestCase {
    var sut: AuthenticationManager!
    
    override func setUp() {
        super.setUp()
        sut = AuthenticationManager.shared
        // Clear any existing auth state before each test
        sut.clearAuthState()
    }
    
    override func tearDown() {
        sut.clearAuthState()
        sut = nil
        super.tearDown()
    }
    
    // MARK: - Initial State Tests
    
    func testAuthenticationManagerInitialState() {
        // Given a fresh authentication manager
        // When checking initial state
        // Then user should not be authenticated
        XCTAssertFalse(sut.isAuthenticated)
        XCTAssertNil(sut.currentUser)
        XCTAssertNil(sut.authError)
    }
    
    // MARK: - Token Storage Tests
    
    func testStoreAndRetrieveAuthState() {
        // Given a user info model
        let userInfo = UserInfosModel(
            id: "123",
            email: "<EMAIL>",
            token: "test-token",
            firstName: "Test",
            lastName: "User"
        )
        
        // When storing auth state
        sut.storeAuthState(userInfo: userInfo)
        
        // Then auth state should be persisted
        XCTAssertTrue(sut.isAuthenticated)
        XCTAssertEqual(sut.currentUser?.id, "123")
        XCTAssertEqual(sut.currentUser?.token, "test-token")
        
        // And when creating a new instance
        let newAuthManager = AuthenticationManager()
        
        // Then it should load the persisted state
        XCTAssertTrue(newAuthManager.isAuthenticated)
        XCTAssertEqual(newAuthManager.currentUser?.id, "123")
    }
    
    func testClearAuthState() {
        // Given a stored auth state
        let userInfo = UserInfosModel(
            id: "123",
            email: "<EMAIL>",
            token: "test-token",
            firstName: "Test",
            lastName: "User"
        )
        sut.storeAuthState(userInfo: userInfo)
        
        // When clearing auth state
        sut.clearAuthState()
        
        // Then auth state should be cleared
        XCTAssertFalse(sut.isAuthenticated)
        XCTAssertNil(sut.currentUser)
        
        // And when creating a new instance
        let newAuthManager = AuthenticationManager()
        
        // Then it should not have any auth state
        XCTAssertFalse(newAuthManager.isAuthenticated)
        XCTAssertNil(newAuthManager.currentUser)
    }
    
    // MARK: - ASAuthorizationControllerDelegate Tests
    
    func testAuthorizationControllerDidCompleteWithValidAuthorization() {
        // Given a mock authorization with valid credential
        let credential = MockASAuthorizationAppleIDCredential(
            user: "test-user",
            email: "<EMAIL>",
            fullName: PersonNameComponents(givenName: "Test", familyName: "User"),
            identityToken: "valid-token".data(using: .utf8)
        )
        let authorization = MockASAuthorization(credential: credential)
        let controller = ASAuthorizationController(authorizationRequests: [])
        
        // When the delegate method is called
        sut.authorizationController(controller: controller, didCompleteWithAuthorization: authorization)
        
        // Then it should not immediately set an error (validation happens async)
        // Note: We can't test the full flow without mocking the API client
        XCTAssertNil(sut.authError) // Should be nil initially
    }
    
    func testAuthorizationControllerDidCompleteWithInvalidCredential() {
        // Given a mock authorization with invalid credential type
        let credential = MockASPasswordCredential()
        let authorization = MockASAuthorization(credential: credential)
        let controller = ASAuthorizationController(authorizationRequests: [])
        
        // When the delegate method is called
        sut.authorizationController(controller: controller, didCompleteWithAuthorization: authorization)
        
        // Then it should set an error
        XCTAssertEqual(sut.authError, "Invalid credential type")
    }
    
    func testAuthorizationControllerDidCompleteWithMissingToken() {
        // Given a credential without identity token
        let credential = MockASAuthorizationAppleIDCredential(
            user: "test-user",
            email: nil,
            fullName: nil,
            identityToken: nil
        )
        let authorization = MockASAuthorization(credential: credential)
        let controller = ASAuthorizationController(authorizationRequests: [])
        
        // When the delegate method is called
        sut.authorizationController(controller: controller, didCompleteWithAuthorization: authorization)
        
        // Then it should set an error
        XCTAssertEqual(sut.authError, "Unable to extract identity token")
    }
    
    func testAuthorizationControllerDidCompleteWithCancelledError() {
        // Given a cancellation error
        let error = ASAuthorizationError(.canceled)
        let controller = ASAuthorizationController(authorizationRequests: [])
        
        // When the delegate method is called
        sut.authorizationController(controller: controller, didCompleteWithError: error)
        
        // Then it should not set an error (user cancelled)
        XCTAssertNil(sut.authError)
    }
    
    func testAuthorizationControllerDidCompleteWithFailedError() {
        // Given a failed error
        let error = ASAuthorizationError(.failed)
        let controller = ASAuthorizationController(authorizationRequests: [])
        
        // When the delegate method is called
        sut.authorizationController(controller: controller, didCompleteWithError: error)
        
        // Then it should set an appropriate error
        XCTAssertEqual(sut.authError, "Authorization failed")
    }
    
    func testAuthorizationControllerDidCompleteWithUnknownError() {
        // Given an unknown error
        let error = ASAuthorizationError(.unknown)
        let controller = ASAuthorizationController(authorizationRequests: [])
        
        // When the delegate method is called
        sut.authorizationController(controller: controller, didCompleteWithError: error)
        
        // Then it should set an appropriate error
        XCTAssertEqual(sut.authError, "An unknown error occurred")
    }
    
    func testAuthorizationControllerDidCompleteWithNonASAuthorizationError() {
        // Given a generic error
        let error = NSError(domain: "TestDomain", code: 123, userInfo: [NSLocalizedDescriptionKey: "Test error"])
        let controller = ASAuthorizationController(authorizationRequests: [])
        
        // When the delegate method is called
        sut.authorizationController(controller: controller, didCompleteWithError: error)
        
        // Then it should set the error description
        XCTAssertEqual(sut.authError, "Test error")
    }
}

// MARK: - Mock Classes

class MockASAuthorizationAppleIDCredential: ASAuthorizationAppleIDCredential {
    private let mockUser: String
    private let mockEmail: String?
    private let mockFullName: PersonNameComponents?
    private let mockIdentityToken: Data?
    
    init(user: String, email: String?, fullName: PersonNameComponents?, identityToken: Data? = nil) {
        self.mockUser = user
        self.mockEmail = email
        self.mockFullName = fullName
        self.mockIdentityToken = identityToken
        super.init(coder: NSCoder())!
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    override var user: String { mockUser }
    override var email: String? { mockEmail }
    override var fullName: PersonNameComponents? { mockFullName }
    override var identityToken: Data? { mockIdentityToken }
}

class MockASPasswordCredential: ASAuthorizationCredential {
    // Mock password credential for testing invalid credential type
}

class MockASAuthorization: ASAuthorization {
    private let mockCredential: ASAuthorizationCredential
    
    init(credential: ASAuthorizationCredential) {
        self.mockCredential = credential
        super.init()
    }
    
    override var credential: ASAuthorizationCredential { mockCredential }
} 